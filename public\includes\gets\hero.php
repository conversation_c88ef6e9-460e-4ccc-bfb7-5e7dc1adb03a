<div class="grid gap-[16px] md:grid-cols-12 lg:grid-cols-4 max-md:-mx-[16px]">
    <div class="aspect-[375/156] overflow-hidden md:col-span-9 md:row-span-2 md:aspect-[716/203] md:rounded-[16px] lg:col-span-3 lg:aspect-[986/280]">
        <div class="group relative size-full">
            <!-- Swiper Container -->
            <div class="swiper hero-carousel size-full">
                <div class="swiper-wrapper">
                    <!-- Slide 1 -->
                    <div class="swiper-slide">
                        <a class="relative block aspect-[375/156] size-full overflow-hidden md:aspect-[716/203] lg:aspect-[986/280]"
                         href="javascript:void(0);">
                         <img alt="Hero Slide 1" loading="eager" decoding="async" data-nimg="fill" class="size-full object-cover" sizes="100vw"
                          src="https://cdn3.upanh.info/upload/server-sw3/advertise-config-b0RhdjlaV3ZZSmNBV0lvQTBRaHVDK1IrNTVEemlpVzZhUkkyaDFUMzhBUT0=/images/giveaway8(9).png"
                          style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                        </a>
                    </div>
                    <!-- Slide 2 -->
                    <div class="swiper-slide">
                        <a class="relative block aspect-[375/156] size-full overflow-hidden md:aspect-[716/203] lg:aspect-[986/280]"
                         href="javascript:void(0);">
                         <img alt="Hero Slide 2" loading="lazy" decoding="async" data-nimg="fill" class="size-full object-cover" sizes="100vw"
                          data-src="https://cdn3.upanh.info/upload/server-sw3/advertise-config-b0RhdjlaV3ZZSmNBV0lvQTBRaHVDK1IrNTVEemlpVzZhUkkyaDFUMzhBUT0=/images/giveaway18(2).png"
                          style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;">
                        </a>
                    </div>
                </div>
            </div>

            <!-- Custom Navigation Arrows -->
            <div class="hero-carousel-prev absolute bottom-0 left-0 top-0 flex h-full w-[120px] cursor-pointer items-center justify-start px-[16px] opacity-0 group-hover:opacity-[1] transition-opacity duration-300 z-[5]">
                <button type="button" class="flex items-center justify-center border pb-[8px] pt-[10px] text-[16px] font-[400]
                 leading-none transition-all duration-300 px-[24px] border-transparent
                 hover:text-[#4B7DFF] rounded h-[32px]">
                 <span class="inline-flex items-center text-center align-[-.125em]">
                  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 6 10" fill="none"><path d="m5 8.781-4-4 4-4" stroke="currentColor" stroke-width="1.5"></path></svg>
                </span>
                </button>
            </div>
            <div class="hero-carousel-next absolute bottom-0 right-0 top-0 flex h-full w-[120px] cursor-pointer items-center justify-end px-[16px] opacity-0 group-hover:opacity-[1] transition-opacity duration-300 z-[5]">
                <button type="button" class="flex items-center justify-center border pb-[8px] pt-[10px] text-[16px] font-[400] leading-none transition-all duration-300 px-[24px] border-transparent hover:text-[#4B7DFF] rounded h-[32px]">
                  <span class="inline-flex items-center text-center align-[-.125em] text-[28px] text-[#fff]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" fill="none"><path d="m6 4 4 4-4 4" stroke="currentColor" stroke-width="1.5"></path></svg>
                  </span>
                </button>
            </div>

            <!-- Custom Pagination Dots -->
            <div class="hero-carousel-pagination absolute bottom-[12px] left-1/2 flex -translate-x-1/2 items-center gap-[8px] z-[5]">
                <!-- Pagination dots will be dynamically generated here -->
            </div>
        </div>
    </div>
    <a class="relative hidden overflow-hidden rounded-[16px] max-lg:col-span-3 md:block md:aspect-[228/94] lg:aspect-[318/132]"
     href="/">
     <img alt="Banner 1" loading="lazy" width="318" height="132" decoding="async" data-nimg="1"
     class="max-lg:col-span-3 md:aspect-[228/94] lg:aspect-[318/132]"
     data-src="https://cdn.divineshop.vn/image/catalog/Anh/VPN-73434.png?hash=1746457420"
     style="color: transparent;">
    </a>
    <a class="relative hidden overflow-hidden rounded-[16px] max-lg:col-span-3 md:block md:aspect-[228/94] lg:aspect-[318/132]" 
    href="/">
    <img alt="Banner 2" loading="lazy" width="318" height="132" decoding="async" data-nimg="1"
    class="max-lg:col-span-3 md:aspect-[228/94] lg:aspect-[318/132]"
    data-src="https://cdn.divineshop.vn/image/catalog/Anh/VPN-73434.png?hash=1746457420"
     style="color: transparent;">
    </a>
    <a class="relative hidden overflow-hidden rounded-[16px] max-lg:col-span-3 md:block md:aspect-[228/94] lg:aspect-[318/132]"
     href="https://muakey.com/categories/phan-mem-hoc-tap?utm_source=home&amp;utm_medium=banner">
     <img alt="Banner 3" loading="lazy" width="318" height="132" decoding="async" data-nimg="1"
     class="max-lg:col-span-3 md:aspect-[228/94] lg:aspect-[318/132]"
     data-src="https://cdn.divineshop.vn/image/catalog/Anh/VPN-73434.png?hash=1746457420" style="color: transparent;">
    </a>
    <a class="relative hidden overflow-hidden rounded-[16px] max-lg:col-span-3 md:block md:aspect-[228/94] lg:aspect-[318/132]"
     href="https://muakey.com/categories/phan-mem-hoc-tap?utm_source=home&amp;utm_medium=banner">
     <img alt="" loading="lazy" width="318" height="132" decoding="async" data-nimg="1" 
     class="max-lg:col-span-3 md:aspect-[228/94] lg:aspect-[318/132]"
     data-src="https://cdn.divineshop.vn/image/catalog/Anh/VPN-73434.png?hash=1746457420" style="color: transparent;">
    </a>
        <a class="relative hidden overflow-hidden rounded-[16px] max-lg:col-span-3 md:block md:aspect-[228/94] lg:aspect-[318/132]"
     href="https://muakey.com/categories/phan-mem-hoc-tap?utm_source=home&amp;utm_medium=banner">
     <img alt="" loading="lazy" width="318" height="132" decoding="async" data-nimg="1" 
     class="max-lg:col-span-3 md:aspect-[228/94] lg:aspect-[318/132]"
     data-src="https://cdn.divineshop.vn/image/catalog/Anh/VPN-73434.png?hash=1746457420" style="color: transparent;">
    </a>
        <a class="relative hidden overflow-hidden rounded-[16px] max-lg:col-span-3 md:block md:aspect-[228/94] lg:aspect-[318/132]"
     href="https://muakey.com/categories/phan-mem-hoc-tap?utm_source=home&amp;utm_medium=banner">
     <img alt="" loading="lazy" width="318" height="132" decoding="async" data-nimg="1"
     class="max-lg:col-span-3 md:aspect-[228/94] lg:aspect-[318/132]"
     data-src="https://cdn.divineshop.vn/image/catalog/Anh/VPN-73434.png?hash=1746457420" style="color: transparent;">
    </a>
</div>
<!-- Swiper.js JavaScript -->


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Hero Carousel
    const heroCarousel = new Swiper('.hero-carousel', {
        // Basic settings
        loop: true,
        autoplay: {
            delay: 4500,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
        },
        speed: 800,
        effect: 'slide',

        // Responsive breakpoints
        breakpoints: {
            // Mobile
            320: {
                slidesPerView: 1,
                spaceBetween: 0,
                allowTouchMove: true,
            },
            // Tablet
            768: {
                slidesPerView: 1,
                spaceBetween: 0,
                allowTouchMove: true,
            },
            // Desktop
            1024: {
                slidesPerView: 1,
                spaceBetween: 0,
                allowTouchMove: true,
            }
        },

        // Custom navigation
        navigation: {
            nextEl: '.hero-carousel-next',
            prevEl: '.hero-carousel-prev',
        },

        // Disable default pagination (we'll use custom)
        pagination: false,

        // Events
        on: {
            init: function() {
                // Create custom pagination dots
                createCustomPagination(this);
                // Set initial active state
                updatePaginationState(this);
            },
            slideChange: function() {
                // Update pagination state on slide change
                updatePaginationState(this);
            },
            autoplayTimeLeft: function(s, time, progress) {
                // Update progress bar animation
                const activeDot = document.querySelector('.hero-carousel-pagination .pagination-dot.active');
                if (activeDot) {
                    const progressPercent = (1 - progress) * 100;
                    activeDot.style.setProperty('--progress', progressPercent + '%');
                }
            }
        }
    });

    // Helper function to create custom pagination dots
    function createCustomPagination(swiper) {
        const paginationContainer = document.querySelector('.hero-carousel-pagination');
        if (!paginationContainer) return;

        // Clear existing dots
        paginationContainer.innerHTML = '';

        // Create dots based on number of slides
        for (let i = 0; i < swiper.slides.length; i++) {
            const dot = document.createElement('div');
            dot.className = 'pagination-dot relative h-[4px] cursor-pointer overflow-hidden rounded-[10px] bg-[#FFFFFF7A] transition-all before:absolute before:top-0 before:block before:h-full before:w-0 before:bg-[#fff] before:content-[""] w-[18px] before:w-0';

            // Add click event to navigate to specific slide
            dot.addEventListener('click', () => {
                swiper.slideTo(i);
            });

            paginationContainer.appendChild(dot);
        }
    }

    // Helper function to update pagination state
    function updatePaginationState(swiper) {
        const dots = document.querySelectorAll('.hero-carousel-pagination .pagination-dot');

        // Remove active class from all dots
        dots.forEach((dot, index) => {
            dot.classList.remove('active');
            dot.classList.remove('w-[36px]');
            dot.classList.add('w-[18px]');

            // Reset progress animation
            const beforeElement = dot.querySelector('::before');
            if (beforeElement) {
                beforeElement.style.width = '0';
            }
        });

        // Add active class to current dot
        if (dots[swiper.realIndex]) {
            const activeDot = dots[swiper.realIndex];
            activeDot.classList.add('active');
            activeDot.classList.remove('w-[18px]');
            activeDot.classList.add('w-[36px]');
        }
    }

    // Pause autoplay on hover
    const carouselContainer = document.querySelector('.hero-carousel');
    if (carouselContainer) {
        carouselContainer.addEventListener('mouseenter', () => {
            heroCarousel.autoplay.stop();
        });

        carouselContainer.addEventListener('mouseleave', () => {
            heroCarousel.autoplay.start();
        });
    }
});
</script>
<ul class="grid grid-flow-row grid-cols-4 rounded-[16px] text-[12px] text-[#fff] max-lg:gap-y-[12px] sm:text-[14px] md:grid-cols-7 md:bg-[#ffffff14] md:text-[16px] md:text-[#fff9] mt-[24px] max-md:!-mx-[16px]">
    <li class="text-center md:flex-1 md:py-[16px]">
      <a class="group flex flex-col items-center gap-[8px]" title="PUBG PC" href="/pubg-pc">
        <span class="flex h-[40px] w-[40px] items-center justify-center rounded-[12px] border border-[#FFFFFF1F] 
        bg-[#FFFFFF1F] md:h-[44px] md:w-[44px] md:border-none md:bg-transparent md:transition-all 
        md:duration-300 md:group-hover:scale-[1.25]">
        <span class="relative block h-[32px] w-[32px] md:h-full md:w-full">
        <img alt="PUBG PC" loading="lazy" width="44" height="44" decoding="async" data-nimg="1" class="absolute aspect-square" 
        style="color:transparent"
        data-src="/images/navigation-icons/pubg-pc.png"></span></span>
        <span class="w-3/4 truncate font-[400] leading-none transition-all duration-300 max-lg:text-[14px] 
        md:leading-[15px] md:group-hover:text-[#fff]">PUBG PC</span>
        </a>
    </li>
    <li class="text-center md:flex-1 md:py-[16px]"><a class="group flex flex-col items-center gap-[8px]" title="Game PC" href="/game-pc"><span class="flex h-[40px] w-[40px] items-center justify-center rounded-[12px] border border-[#FFFFFF1F] bg-[#FFFFFF1F] md:h-[44px] md:w-[44px] md:border-none md:bg-transparent md:transition-all md:duration-300 md:group-hover:scale-[1.25]"><span class="relative block h-[32px] w-[32px] md:h-full md:w-full"><img alt="Game PC" loading="lazy" width="44" height="44" decoding="async" data-nimg="1" class="absolute aspect-square" style="color:transparent" src="/images/navigation-icons/game-pc.png"></span></span><span class="w-3/4 truncate font-[400] leading-none transition-all duration-300 max-lg:text-[14px] md:leading-[15px] md:group-hover:text-[#fff]">Game PC</span></a>
    </li>
    <li class="text-center md:flex-1 md:py-[16px]"><a class="group flex flex-col items-center gap-[8px]" title="Game Mobile" href="/game-mobile"><span class="flex h-[40px] w-[40px] items-center justify-center rounded-[12px] border border-[#FFFFFF1F] bg-[#FFFFFF1F] md:h-[44px] md:w-[44px] md:border-none md:bg-transparent md:transition-all md:duration-300 md:group-hover:scale-[1.25]"><span class="relative block h-[32px] w-[32px] md:h-full md:w-full"><img alt="Game Mobile" loading="lazy" width="44" height="44" decoding="async" data-nimg="1" class="absolute aspect-square" style="color:transparent" src="/images/navigation-icons/game-mobile.png"></span></span><span class="w-3/4 truncate font-[400] leading-none transition-all duration-300 max-lg:text-[14px] md:leading-[15px] md:group-hover:text-[#fff]">Game Mobile</span></a>
    </li>
    <li class="text-center md:flex-1 md:py-[16px]"><a class="group flex flex-col items-center gap-[8px]" title="Gift Card " href="/gift-card"><span class="flex h-[40px] w-[40px] items-center justify-center rounded-[12px] border border-[#FFFFFF1F] bg-[#FFFFFF1F] md:h-[44px] md:w-[44px] md:border-none md:bg-transparent md:transition-all md:duration-300 md:group-hover:scale-[1.25]"><span class="relative block h-[32px] w-[32px] md:h-full md:w-full"><img alt="Gift Card " loading="lazy" width="44" height="44" decoding="async" data-nimg="1" class="absolute aspect-square" style="color:transparent" src="/images/navigation-icons/gift-card.png"></span></span><span class="w-3/4 truncate font-[400] leading-none transition-all duration-300 max-lg:text-[14px] md:leading-[15px] md:group-hover:text-[#fff]">Gift Card </span></a>
    </li>
    <li class="text-center md:flex-1 md:py-[16px]"><a class="group flex flex-col items-center gap-[8px]" title="Tiện ích" href="/tien-ich"><span class="flex h-[40px] w-[40px] items-center justify-center rounded-[12px] border border-[#FFFFFF1F] bg-[#FFFFFF1F] md:h-[44px] md:w-[44px] md:border-none md:bg-transparent md:transition-all md:duration-300 md:group-hover:scale-[1.25]"><span class="relative block h-[32px] w-[32px] md:h-full md:w-full"><img alt="Tiện ích" loading="lazy" width="44" height="44" decoding="async" data-nimg="1" class="absolute aspect-square" style="color:transparent" src="/images/navigation-icons/tien-ich.png"></span></span><span class="w-3/4 truncate font-[400] leading-none transition-all duration-300 max-lg:text-[14px] md:leading-[15px] md:group-hover:text-[#fff]">Tiện ích</span></a>
    </li>
    <li class="text-center md:flex-1 md:py-[16px]"><a class="group flex flex-col items-center gap-[8px]" title="Tài khoản" href="/tai-khoan"><span class="flex h-[40px] w-[40px] items-center justify-center rounded-[12px] border border-[#FFFFFF1F] bg-[#FFFFFF1F] md:h-[44px] md:w-[44px] md:border-none md:bg-transparent md:transition-all md:duration-300 md:group-hover:scale-[1.25]"><span class="relative block h-[32px] w-[32px] md:h-full md:w-full"><img alt="Tài khoản" loading="lazy" width="44" height="44" decoding="async" data-nimg="1" class="absolute aspect-square" style="color:transparent" src="/images/navigation-icons/tai-khoan.png"></span></span><span class="w-3/4 truncate font-[400] leading-none transition-all duration-300 max-lg:text-[14px] md:leading-[15px] md:group-hover:text-[#fff]">Tài khoản</span></a>
    </li>
    <li class="text-center md:flex-1 md:py-[16px]"><a class="group flex flex-col items-center gap-[8px]" title="Top up" href="/top-up"><span class="flex h-[40px] w-[40px] items-center justify-center rounded-[12px] border border-[#FFFFFF1F] bg-[#FFFFFF1F] md:h-[44px] md:w-[44px] md:border-none md:bg-transparent md:transition-all md:duration-300 md:group-hover:scale-[1.25]"><span class="relative block h-[32px] w-[32px] md:h-full md:w-full"><img alt="Top up" loading="lazy" width="44" height="44" decoding="async" data-nimg="1" class="absolute aspect-square" style="color:transparent" src="/images/navigation-icons/top-up.png"></span></span><span class="w-3/4 truncate font-[400] leading-none transition-all duration-300 max-lg:text-[14px] md:leading-[15px] md:group-hover:text-[#fff]">Top up</span></a>
    </li>
</ul>