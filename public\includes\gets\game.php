<div x-data="gamesData()" x-init="init()" class="grid grid-cols-1 gap-14 pb-10">
    
  <!-- Enhanced Loading Skeleton with Modern Design -->
  <div x-show="loading" x-cloak>
    <div class="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-4 gap-4 xl:gap-6">
      <template x-for="n in 8" :key="n">
        <div class="col-span-1 relative bg-gradient-to-br from-[#13112E] to-[#1a1640] border border-[#5081FF33] rounded-2xl p-4 shadow-lg animate-pulse overflow-hidden">
          <!-- Background Pattern -->
          <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#5081FF] to-transparent transform -skew-y-1"></div>
          </div>

          <!-- Skeleton Image -->
          <div class="relative w-full h-44 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] mb-4 rounded-xl overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"></div>
          </div>

          <!-- Skeleton Title -->
          <div class="h-5 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] w-3/4 mb-3 rounded-lg"></div>

          <!-- Skeleton Stats -->
          <div class="space-y-2 mb-4">
            <div class="h-3 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] w-1/2 rounded"></div>
            <div class="h-3 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] w-2/3 rounded"></div>
          </div>

          <!-- Skeleton Button -->
          <div class="h-10 bg-gradient-to-r from-[#4B7DFF] to-[#5081FF] rounded-xl"></div>
        </div>
      </template>
    </div>
  </div>
    
  <!-- Nội dung chính sẽ hiển thị khi loading = false -->
  <div x-show="!loading" x-cloak>
    <!-- Lặp qua các game -->
    <template x-if="homepage === 'category'">
    
    <template x-for="game in games" :key="game.id">
      <div>
        <!-- Enhanced Game Title Section -->
        <div class="text-center my-8">
          <div class="relative inline-block">
            <h4 class="font-bold text-white text-3xl xl:text-4xl uppercase tracking-wide relative z-10"
                style="text-shadow: rgb(75, 125, 255) 0px 0px 12px, rgb(80, 129, 255) 0px 0px 24px, rgb(75, 125, 255) 0px 0px 48px;"
                x-text="game.name"></h4>
            <!-- Decorative Background -->
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#4B7DFF]/20 to-transparent blur-xl transform scale-110"></div>
          </div>
          <div class="flex justify-center pt-4">
            <div class="w-24 h-1 bg-gradient-to-r from-[#4B7DFF] to-[#5081FF] rounded-full shadow-lg"></div>
          </div>
        </div>

        <!-- Enhanced Grid Container -->
        <div >
          <div class="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-4 gap-4 xl:gap-6">
        
            <!-- Dynamic Gaming Product Cards with Random Triangular Colors -->
            <template x-for="category in game.categories" :key="category.id">
              <div x-data="{
                topLeftColor: getRandomTriangleColor(),
                bottomRightColor: getRandomTriangleColor(),
                topLeftHoverColor: getRandomTriangleColor(),
                bottomRightHoverColor: getRandomTriangleColor(),
                isHovered: false
              }"
              style="background: rgb(39 36 80/var(--tw-bg-opacity,1));"
              @mouseenter="isHovered = true"
              @mouseleave="isHovered = false"
              class="group col-span-1 relative bg-[#2b22b3] border-2 border-transparent hover:border-[#5081FF] rounded-xl p-3 shadow-lg hover:shadow-2xl hover:shadow-[#5081FF]/40 transition-all duration-300 ease-in-out transform hover:scale-105 hover:-translate-y-2 overflow-hidden flex flex-col min-h-[380px] gaming-card-hover">
                <!-- Dynamic Triangular Highlight - Top Left (Random Colors) -->
                <div class="triangle-top-left absolute top-0 left-0 w-16 h-16 transition-all duration-500"
                     :class="isHovered ? 'opacity-100' : 'opacity-70'"
                     :style="isHovered ?
                       `clip-path: polygon(0 0, 100% 0, 0 100%); background: linear-gradient(to bottom right, ${topLeftHoverColor}60, ${topLeftHoverColor}40);` :
                       `clip-path: polygon(0 0, 100% 0, 0 100%); background: linear-gradient(to bottom right, ${topLeftColor}40, ${topLeftColor}25);`"
                     ></div>

                <!-- Dynamic Triangular Highlight - Bottom Right (Random Colors) -->
                <div class="triangle-bottom-right absolute bottom-0 right-0 w-12 h-12 transition-all duration-500"
                     :class="isHovered ? 'opacity-100' : 'opacity-60'"
                     :style="isHovered ?
                       `clip-path: polygon(100% 0, 100% 100%, 0 100%); background: linear-gradient(to top left, ${bottomRightHoverColor}50, ${bottomRightHoverColor}30);` :
                       `clip-path: polygon(100% 0, 100% 100%, 0 100%); background: linear-gradient(to top left, ${bottomRightColor}30, ${bottomRightColor}20);`"
                     ></div>

                <!-- Refined Background Pattern - Disabled for border-only hover -->
                <!-- <div class="absolute inset-0 opacity-3 group-hover:opacity-8 transition-opacity duration-500">
                  <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#5081FF] to-transparent transform -skew-y-1"></div>
                </div> -->

                <!-- Compact Image Container -->
                <a class="block relative z-10 flex-shrink-0" :href="`/service/${game.slug}/${category.slug}`">
                  <div x-data="{ loaded: false }" class="relative w-full h-36 rounded-lg overflow-hidden group-hover:scale-[1.02] transition-transform duration-500">
                    <!-- Enhanced Skeleton -->
                    <div x-show="!loaded" x-cloak class="absolute inset-0 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] animate-pulse rounded-lg">
                      <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"></div>
                    </div>

                    <!-- Product Image -->
                    <img @load="loaded = true"
                         class="w-full h-full object-cover rounded-lg transition-all duration-500"
                         width="300px" height="144px"
                         :src="category.images"
                         :alt="category.name">

                    <!-- Subtle Overlay Gradient - Disabled for border-only hover -->
                    <!-- <div class="absolute inset-0 bg-gradient-to-t from-black/15 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg"></div> -->
                  </div>
                </a>

                <!-- Compact Content Section with Flex Layout -->
                <div class="relative z-10 mt-3 flex flex-col flex-grow">
                  <!-- Compact Product Title -->
                  <div class="flex-shrink-0 mb-3">
                    <h4 class="text-[#FFFFFFCC] text-base font-bold text-center leading-5 min-h-[2.5rem] flex items-center justify-center transition-colors duration-300 px-1" x-text="category.name"></h4>
                  </div>

                  <!-- Compact Gaming Stats Grid -->
                  <div class="grid grid-cols-2 gap-2 mb-4 flex-shrink-0">
                    <!-- Regular Product Stats with Gaming Aesthetic -->
                    <template x-if="category.type !== 'dichvu'">
                      <div class="gaming-stat-card group/stat relative bg-gradient-to-br from-[#1a1640]/80 to-[#13112E]/90 rounded-lg p-2 border border-[#4B7DFF]/30 hover:border-[#4B7DFF]/60 transition-all duration-500 overflow-hidden">
                        <!-- Genshin-inspired glow effect - Disabled for border-only hover -->
                        <!-- <div class="absolute inset-0 bg-gradient-to-r from-[#4B7DFF]/5 via-[#5081FF]/10 to-[#4B7DFF]/5 opacity-0 group-hover/stat:opacity-100 transition-opacity duration-500"></div> -->

                        <!-- Valorant-style corner accents -->
                        <div class="absolute top-0 left-0 w-2 h-2 border-l-2 border-t-2 border-[#4B7DFF]/60"></div>
                        <div class="absolute bottom-0 right-0 w-2 h-2 border-r-2 border-b-2 border-[#4B7DFF]/60"></div>

                        <!-- Gaming icon and content -->
                        <div class="relative z-10 flex items-center gap-2">
                          <div class="gaming-icon-sold w-6 h-6 flex items-center justify-center bg-gradient-to-br from-[#FFD700] to-[#FFA500] rounded-md shadow-lg">
                            <svg class="w-3 h-3 text-[#1a1640]" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                            </svg>
                          </div>
                          <div class="flex-1">
                            <div class="text-[10px] text-[#9F9BAB] mb-1 font-medium tracking-wide uppercase opacity-80">ĐÃ BÁN</div>
                            <div class="text-sm font-bold text-[#FFD700] transition-colors duration-300" x-text="category.sold"></div>
                          </div>
                        </div>

                        <!-- Gaming-style progress indicator -->
                        <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#FFD700]/20 via-[#FFD700]/60 to-[#FFD700]/20 transform scale-x-0 group-hover/stat:scale-x-100 transition-transform duration-500"></div>
                      </div>
                    </template>

                    <template x-if="category.type !== 'dichvu'">
                      <div class="gaming-stat-card group/stat relative bg-gradient-to-br from-[#1a1640]/80 to-[#13112E]/90 rounded-lg p-2 border border-[#4B7DFF]/30 hover:border-[#4B7DFF]/60 transition-all duration-500 overflow-hidden">
                        <!-- Genshin-inspired glow effect - Disabled for border-only hover -->
                        <!-- <div class="absolute inset-0 bg-gradient-to-r from-[#4B7DFF]/5 via-[#5081FF]/10 to-[#4B7DFF]/5 opacity-0 group-hover/stat:opacity-100 transition-opacity duration-500"></div> -->

                        <!-- Valorant-style corner accents -->
                        <div class="absolute top-0 left-0 w-2 h-2 border-l-2 border-t-2 border-[#4B7DFF]/60"></div>
                        <div class="absolute bottom-0 right-0 w-2 h-2 border-r-2 border-b-2 border-[#4B7DFF]/60"></div>

                        <!-- Gaming icon and content -->
                        <div class="relative z-10 flex items-center gap-2">
                          <div class="gaming-icon-available w-6 h-6 flex items-center justify-center bg-gradient-to-br from-[#00FF88] to-[#00CC6A] rounded-md shadow-lg">
                            <svg class="w-3 h-3 text-[#1a1640]" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                          </div>
                          <div class="flex-1">
                            <div class="text-[10px] text-[#9F9BAB] mb-1 font-medium tracking-wide uppercase opacity-80">ĐANG BÁN</div>
                            <div class="text-sm font-bold text-[#00FF88] transition-colors duration-300" x-text="category.available"></div>
                          </div>
                        </div>

                        <!-- Gaming-style progress indicator -->
                        <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#00FF88]/20 via-[#00FF88]/60 to-[#00FF88]/20 transform scale-x-0 group-hover/stat:scale-x-100 transition-transform duration-500"></div>
                      </div>
                    </template>

                    <!-- Service Stats with Gaming Aesthetic -->
                    <template x-if="category.type === 'dichvu'">
                      <div class="gaming-stat-card group/stat relative bg-gradient-to-br from-[#1a1640]/80 to-[#13112E]/90 rounded-lg p-2 border border-[#4B7DFF]/30 hover:border-[#4B7DFF]/60 transition-all duration-500 overflow-hidden">
                        <!-- Genshin-inspired glow effect - Disabled for border-only hover -->
                        <!-- <div class="absolute inset-0 bg-gradient-to-r from-[#4B7DFF]/5 via-[#5081FF]/10 to-[#4B7DFF]/5 opacity-0 group-hover/stat:opacity-100 transition-opacity duration-500"></div> -->

                        <!-- Valorant-style corner accents -->
                        <div class="absolute top-0 left-0 w-2 h-2 border-l-2 border-t-2 border-[#4B7DFF]/60"></div>
                        <div class="absolute bottom-0 right-0 w-2 h-2 border-r-2 border-b-2 border-[#4B7DFF]/60"></div>

                        <!-- Gaming icon and content -->
                        <div class="relative z-10 flex items-center gap-2">
                          <div class="gaming-icon-orders w-6 h-6 flex items-center justify-center bg-gradient-to-br from-[#FF6B6B] to-[#FF5252] rounded-md shadow-lg">
                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                          </div>
                          <div class="flex-1">
                            <div class="text-[10px] text-[#9F9BAB] mb-1 font-medium tracking-wide uppercase opacity-80">SỐ ĐƠN</div>
                            <div class="text-sm font-bold text-[#FF6B6B] transition-colors duration-300" x-text="category.orderCount"></div>
                          </div>
                        </div>

                        <!-- Gaming-style progress indicator -->
                        <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#FF6B6B]/20 via-[#FF6B6B]/60 to-[#FF6B6B]/20 transform scale-x-0 group-hover/stat:scale-x-100 transition-transform duration-500"></div>
                      </div>
                    </template>

                    <template x-if="category.type === 'dichvu'">
                      <div class="gaming-stat-card group/stat relative bg-gradient-to-br from-[#1a1640]/80 to-[#13112E]/90 rounded-lg p-2 border border-[#4B7DFF]/30 hover:border-[#4B7DFF]/60 transition-all duration-500 overflow-hidden">
                        <!-- Genshin-inspired glow effect - Disabled for border-only hover -->
                        <!-- <div class="absolute inset-0 bg-gradient-to-r from-[#4B7DFF]/5 via-[#5081FF]/10 to-[#4B7DFF]/5 opacity-0 group-hover/stat:opacity-100 transition-opacity duration-500"></div> -->

                        <!-- Valorant-style corner accents -->
                        <div class="absolute top-0 left-0 w-2 h-2 border-l-2 border-t-2 border-[#4B7DFF]/60"></div>
                        <div class="absolute bottom-0 right-0 w-2 h-2 border-r-2 border-b-2 border-[#4B7DFF]/60"></div>

                        <!-- Gaming icon and content -->
                        <div class="relative z-10 flex items-center gap-2">
                          <div class="gaming-icon-completed w-6 h-6 flex items-center justify-center bg-gradient-to-br from-[#9C27B0] to-[#7B1FA2] rounded-md shadow-lg">
                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                          </div>
                          <div class="flex-1">
                            <div class="text-[10px] text-[#9F9BAB] mb-1 font-medium tracking-wide uppercase opacity-80">HOÀN THÀNH</div>
                            <div class="text-sm font-bold text-[#9C27B0] transition-colors duration-300" x-text="category.completed"></div>
                          </div>
                        </div>

                        <!-- Gaming-style progress indicator -->
                        <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-[#9C27B0]/20 via-[#9C27B0]/60 to-[#9C27B0]/20 transform scale-x-0 group-hover/stat:scale-x-100 transition-transform duration-500"></div>
                      </div>
                    </template>
                  </div>

                  <!-- Compact Gaming Action Button -->
                  <div class="mt-auto">
                    <a class="block w-full" :href="`/service/${game.slug}/${category.slug}`">
                    <button class="gaming-action-btn group/btn relative w-full bg-gradient-to-r from-[#4B7DFF] via-[#5081FF] to-[#4B7DFF] hover:from-[#5081FF] hover:via-[#4B7DFF] hover:to-[#5081FF] text-white font-bold py-3 px-4 rounded-lg transition-all duration-500 transform hover:scale-[1.02] hover:shadow-xl hover:shadow-[#4B7DFF]/30 border border-[#5081FF]/50 hover:border-[#4B7DFF]/80 overflow-hidden">
                      <!-- Genshin Impact inspired ethereal glow - Disabled for border-only hover -->
                      <!-- <div class="absolute inset-0 bg-gradient-to-r from-[#4B7DFF]/20 via-[#5081FF]/30 to-[#4B7DFF]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-500 rounded-xl"></div> -->

                      <!-- Valorant-style animated border lines -->
                      <div class="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#00FFFF] to-transparent transform -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>
                      <div class="absolute bottom-0 right-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#00FFFF] to-transparent transform translate-x-full group-hover/btn:-translate-x-full transition-transform duration-700"></div>

                      <!-- Gaming-style corner brackets - Disabled for border-only hover -->
                      <!-- <div class="absolute top-1 left-1 w-3 h-3 border-l-2 border-t-2 border-[#00FFFF]/60 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                      <div class="absolute top-1 right-1 w-3 h-3 border-r-2 border-t-2 border-[#00FFFF]/60 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                      <div class="absolute bottom-1 left-1 w-3 h-3 border-l-2 border-b-2 border-[#00FFFF]/60 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                      <div class="absolute bottom-1 right-1 w-3 h-3 border-r-2 border-b-2 border-[#00FFFF]/60 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div> -->

                      <!-- Button content with gaming typography -->
                      <span class="relative z-10 flex items-center justify-center gap-3">
                        <!-- Gaming-style play icon -->
                        <div class="w-6 h-6 bg-gradient-to-br from-[#00FFFF] to-[#0099CC] rounded-md flex items-center justify-center shadow-lg group-hover/btn:shadow-[#00FFFF]/50 transition-all duration-300">
                          <svg class="w-3 h-3 text-[#1a1640] ml-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
                          </svg>
                        </div>

                        <!-- Gaming-style text with effects -->
                        <span class="text-sm font-black uppercase tracking-[0.2em] text-shadow-gaming transition-colors duration-300">
                          XEM NGAY
                        </span>

                        <!-- Animated arrow with particle effect -->
                        <div class="relative">
                          <svg class="w-5 h-5 transition-all duration-300 group-hover/btn:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                          </svg>
                          <!-- Particle trail effect - Disabled for border-only hover -->
                          <!-- <div class="absolute -right-1 top-1/2 w-1 h-1 bg-[#00FFFF] rounded-full opacity-0 group-hover/btn:opacity-100 group-hover/btn:animate-ping transition-opacity duration-300"></div> -->
                        </div>
                      </span>

                      <!-- Gaming-style pulse effect on hover - Disabled for border-only hover -->
                      <!-- <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-[#4B7DFF]/10 to-[#5081FF]/10 opacity-0 group-hover/btn:opacity-100 group-hover/btn:animate-pulse transition-opacity duration-500"></div> -->
                    </button>
                    </a>
                  </div>
                </div>
              </div>
            </template>
         
          </div>
        </div>
      </div>
    </template>

  </template>

  <template x-if="homepage != 'category'">
    
    
      <div>
        <!-- Enhanced Game List Title -->
        <div class="text-center mb-8">
          <div class="relative inline-block">
            <h4 class="font-bold text-white text-3xl xl:text-4xl uppercase tracking-wide relative z-10"
                style="text-shadow: rgb(75, 125, 255) 0px 0px 12px, rgb(80, 129, 255) 0px 0px 24px, rgb(75, 125, 255) 0px 0px 48px;">
              Danh Mục Game
            </h4>
            <!-- Decorative Background -->
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#4B7DFF]/20 to-transparent blur-xl transform scale-110"></div>
          </div>
          <div class="flex justify-center pt-4">
            <div class="w-24 h-1 bg-gradient-to-r from-[#4B7DFF] to-[#5081FF] rounded-full shadow-lg"></div>
          </div>
        </div>

        <!-- Enhanced Game Grid Container -->
        <div >
          <div class="grid grid-cols-1 md:grid-cols-3 xl:grid-cols-4 gap-4 xl:gap-6">
        
            <!-- Dynamic Game Cards with Random Triangular Colors -->
            <template x-for="game in games" :key="game.id">
              <div x-data="{
                topLeftColor: getRandomTriangleColor(),
                bottomRightColor: getRandomTriangleColor(),
                topLeftHoverColor: getRandomTriangleColor(),
                bottomRightHoverColor: getRandomTriangleColor(),
                isHovered: false
              }"
              @mouseenter="isHovered = true"
              @mouseleave="isHovered = false"
              class="group col-span-1 relative bg-[#272450] border-2 border-transparent hover:border-[#5081FF] rounded-xl p-3 shadow-lg hover:shadow-2xl hover:shadow-[#5081FF]/40 transition-all duration-300 ease-in-out transform hover:scale-105 hover:-translate-y-2 overflow-hidden flex flex-col min-h-[320px] gaming-card-hover">
                <!-- Dynamic Triangular Highlight - Top Left (Random Colors) -->
                <div class="triangle-top-left absolute top-0 left-0 w-14 h-14 transition-all duration-500"
                     :class="isHovered ? 'opacity-100' : 'opacity-65'"
                     :style="isHovered ?
                       `clip-path: polygon(0 0, 100% 0, 0 100%); background: linear-gradient(to bottom right, ${topLeftHoverColor}55, ${topLeftHoverColor}35);` :
                       `clip-path: polygon(0 0, 100% 0, 0 100%); background: linear-gradient(to bottom right, ${topLeftColor}35, ${topLeftColor}20);`"
                     ></div>

                <!-- Dynamic Triangular Highlight - Bottom Right (Random Colors) -->
                <div class="triangle-bottom-right absolute bottom-0 right-0 w-10 h-10 transition-all duration-500"
                     :class="isHovered ? 'opacity-100' : 'opacity-55'"
                     :style="isHovered ?
                       `clip-path: polygon(100% 0, 100% 100%, 0 100%); background: linear-gradient(to top left, ${bottomRightHoverColor}45, ${bottomRightHoverColor}25);` :
                       `clip-path: polygon(100% 0, 100% 100%, 0 100%); background: linear-gradient(to top left, ${bottomRightColor}25, ${bottomRightColor}15);`"
                     ></div>

                <!-- Refined Background Pattern - Disabled for border-only hover -->
                <!-- <div class="absolute inset-0 opacity-3 group-hover:opacity-8 transition-opacity duration-500">
                  <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#5081FF] to-transparent transform -skew-y-1"></div>
                </div> -->

                <!-- Compact Image Container -->
                <a class="block relative z-10 flex-shrink-0" :href="`/game/${game.slug}`">
                  <div x-data="{ loaded: false }" class="relative w-full h-36 rounded-lg overflow-hidden group-hover:scale-[1.02] transition-transform duration-500">
                    <!-- Enhanced Skeleton -->
                    <div x-show="!loaded" x-cloak class="absolute inset-0 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] animate-pulse rounded-xl">
                      <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"></div>
                    </div>

                    <!-- Game Image -->
                    <img @load="loaded = true"
                         class="w-full h-full object-cover rounded-xl transition-all duration-500"
                         width="300px" height="176px"
                         :src="game.images"
                         :alt="game.name" loading="lazy">

                    <!-- Overlay Gradient - Disabled for border-only hover -->
                    <!-- <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl"></div> -->
                  </div>
                </a>

                <!-- Content Section with Flex Layout -->
                <div class="relative z-10 mt-4 flex flex-col flex-grow">
                  <!-- Game Title with Consistent Height -->
                  <div class="flex-grow flex items-center justify-center mb-6">
                    <h4 class="text-[#FFFFFFCC] text-lg font-bold text-center leading-6 transition-colors duration-300 px-2" x-text="game.name"></h4>
                  </div>

                  <!-- Premium Gaming Action Button -->
                  <a class="block w-full" :href="`/game/${game.slug}`">
                    <button class="gaming-action-btn group/btn relative w-full bg-gradient-to-r from-[#4B7DFF] via-[#5081FF] to-[#4B7DFF] hover:from-[#5081FF] hover:via-[#4B7DFF] hover:to-[#5081FF] text-white font-bold py-4 px-6 rounded-xl transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-[#4B7DFF]/40 border-2 border-[#5081FF]/50 hover:border-[#4B7DFF]/80 overflow-hidden">
                      <!-- Genshin Impact inspired ethereal glow - Disabled for border-only hover -->
                      <!-- <div class="absolute inset-0 bg-gradient-to-r from-[#4B7DFF]/20 via-[#5081FF]/30 to-[#4B7DFF]/20 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-500 rounded-xl"></div> -->

                      <!-- Valorant-style animated border lines -->
                      <div class="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#00FFFF] to-transparent transform -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>
                      <div class="absolute bottom-0 right-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#00FFFF] to-transparent transform translate-x-full group-hover/btn:-translate-x-full transition-transform duration-700"></div>

                      <!-- Gaming-style corner brackets - Disabled for border-only hover -->
                      <!-- <div class="absolute top-1 left-1 w-3 h-3 border-l-2 border-t-2 border-[#00FFFF]/60 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                      <div class="absolute top-1 right-1 w-3 h-3 border-r-2 border-t-2 border-[#00FFFF]/60 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                      <div class="absolute bottom-1 left-1 w-3 h-3 border-l-2 border-b-2 border-[#00FFFF]/60 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                      <div class="absolute bottom-1 right-1 w-3 h-3 border-r-2 border-b-2 border-[#00FFFF]/60 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div> -->

                      <!-- Button content with gaming typography -->
                      <span class="relative z-10 flex items-center justify-center gap-3">
                        <!-- Gaming-style grid icon -->
                        <div class="w-6 h-6 bg-gradient-to-br from-[#00FFFF] to-[#0099CC] rounded-md flex items-center justify-center shadow-lg group-hover/btn:shadow-[#00FFFF]/50 transition-all duration-300">
                          <svg class="w-3 h-3 text-[#1a1640]" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                          </svg>
                        </div>

                        <!-- Gaming-style text with effects -->
                        <span class="text-sm font-black uppercase tracking-[0.2em] text-shadow-gaming transition-colors duration-300">
                          XEM TẤT CẢ
                        </span>

                        <!-- Animated arrow with particle effect -->
                        <div class="relative">
                          <svg class="w-5 h-5 transition-all duration-300 group-hover/btn:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                          </svg>
                          <!-- Particle trail effect - Disabled for border-only hover -->
                          <!-- <div class="absolute -right-1 top-1/2 w-1 h-1 bg-[#00FFFF] rounded-full opacity-0 group-hover/btn:opacity-100 group-hover/btn:animate-ping transition-opacity duration-300"></div> -->
                        </div>
                      </span>

                      <!-- Gaming-style pulse effect on hover - Disabled for border-only hover -->
                      <!-- <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-[#4B7DFF]/10 to-[#5081FF]/10 opacity-0 group-hover/btn:opacity-100 group-hover/btn:animate-pulse transition-opacity duration-500"></div> -->
                    </button>
                  </a>
                </div>
              </div>
            </template>
         
          </div>
        </div>

      </div>
   

  </template>
  </div>
    
</div>

<!-- Enhanced Custom Styles for Game Cards -->


<!-- Gaming Color Randomization System -->
<script>
  // Gaming-appropriate color palette for triangular highlights
  const gamingColorPalette = [
    // Blue variants
    '#4B7DFF', '#5081FF', '#00FFFF',
    // Purple variants
    '#9C27B0', '#7B1FA2', '#8E24AA',
    // Green variants
    '#00FF88', '#00CC6A', '#4CAF50',
    // Gold variants
    '#FFD700', '#FFA500', '#FF8F00',
    // Red variants
    '#FF6B6B', '#FF5252', '#F44336',
    // Additional gaming colors
    '#E91E63', '#673AB7', '#3F51B5', '#2196F3', '#009688', '#8BC34A', '#CDDC39', '#FFC107', '#FF9800', '#795548'
  ];

  // Function to get random color from gaming palette
  function getRandomTriangleColor() {
    const randomIndex = Math.floor(Math.random() * gamingColorPalette.length);
    return gamingColorPalette[randomIndex];
  }

  // Enhanced hover effect system for dynamic triangular colors
  document.addEventListener('DOMContentLoaded', function() {
    // Add CSS for dynamic hover effects
    const style = document.createElement('style');
    style.textContent = `
      .dynamic-triangle-hover:hover {
        transition: all 0.5s ease-in-out !important;
      }

      .dynamic-triangle-hover:hover [data-hover-style] {
        background: var(--hover-bg) !important;
      }
    `;
    document.head.appendChild(style);
  });
</script>

<!-- Script Alpine.js với hàm gọi API và hiệu ứng loading -->
<script>
  function gamesData() {
    return {
      games: [],
      homepage: '',
      loading: true,
      init() {
        fetch('/api/game/list')
          .then(response => response.json())
          .then(data => {
            // Gán dữ liệu trả về cho biến
            this.homepage = data.homepage;
            this.games = data.games;
          })
          .catch(error => console.error('Error fetching data:', error))
          .finally(() => {
            this.loading = false;
          });
      }
    }
  }
</script>
