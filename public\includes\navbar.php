<?php
    require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/sidebar/top.php';
?>
        <div style="">
          <div class="bg-[#0E0A2F]">
            <div class="relative border-y border-y-[#5081ff33]">
              <div class="container">
                <div class="flex items-center justify-between gap-[12px] py-[16px]">
                  <div class="flex items-center gap-3 overflow-hidden">
                    <a class="inline-block" href="/">
                      <img src="<?php echo $duogxaolin->site('logo') ?>" alt="<?php echo $duogxaolin->site('title') ?>" class="logo h-6 w-auto object-contain transition-all duration-300 hover:scale-105 hover:brightness-110">

                          <path d="m0 0 9.522 9.404h.05L19.096 0v19.712h-3.417l.025-11.461h-.101l-6.054 5.97-6.03-5.97h-.101l.026 11.46H0zm35.552 5.192v14.52H32.31V17.63h-.05q-.577 1.054-1.72 1.781-1.145.727-2.802.727-1.257 0-2.286-.452a5.15 5.15 0 0 1-1.77-1.267 5.8 5.8 0 0 1-1.156-1.956 7.3 7.3 0 0 1-.415-2.495V5.192h3.518v8.325q0 .678.251 1.254.252.576.679 1.016.427.439 1.018.69.59.25 1.243.25.678 0 1.256-.25a3.27 3.27 0 0 0 1.709-1.705q.25-.579.251-1.255V5.192zm14.75 12.388h-.05a8 8 0 0 1-.867.966 5.84 5.84 0 0 1-2.425 1.38 5.7 5.7 0 0 1-1.584.213 7.07 7.07 0 0 1-5.163-2.193 7.8 7.8 0 0 1-1.57-2.445q-.579-1.404-.578-3.06 0-1.654.578-3.058a7.7 7.7 0 0 1 1.57-2.433 7.13 7.13 0 0 1 5.163-2.181q.855 0 1.584.2.729.202 1.332.553t1.093.815.867.966h.05V5.192h3.493v14.52h-3.493zm0-5.141q-.001-.878-.326-1.655a4.2 4.2 0 0 0-.905-1.354 4.4 4.4 0 0 0-1.357-.916 4.2 4.2 0 0 0-1.684-.34q-.88 0-1.658.34-.78.337-1.356.916-.578.577-.918 1.354a4.1 4.1 0 0 0-.34 1.655q0 .877.34 1.667.339.79.918 1.366.578.577 1.356.916.78.338 1.658.339.906 0 1.684-.34.779-.337 1.357-.915.578-.577.905-1.366a4.3 4.3 0 0 0 .326-1.667m9.95-.928.05.026 5.729-6.345h4.723l-7.337 8.2 7.361 6.318h-5.352l-4.448-3.661-4.17 4.563V.902h3.442v10.609zm26.633.928q0 1.655-.604 3.059a7.6 7.6 0 0 1-1.646 2.432 7.6 7.6 0 0 1-2.475 1.617 8 8 0 0 1-3.09.59 8.1 8.1 0 0 1-3.08-.59 8 8 0 0 1-2.525-1.617 7.6 7.6 0 0 1-1.708-2.432q-.629-1.404-.628-3.06 0-1.653.628-3.046a7.7 7.7 0 0 1 1.708-2.42 8 8 0 0 1 2.526-1.617 8.1 8.1 0 0 1 3.078-.59q2.137.027 3.844.953a7.2 7.2 0 0 1 2.713 2.533l-8.19 8.15q.401.176.766.252.364.075.867.076.906 0 1.683-.34.779-.338 1.37-.928a4.4 4.4 0 0 0 .93-1.367q.339-.778.34-1.655zm-12.06 0q0 .502.113.94t.29.866l5.678-5.643a5 5 0 0 0-.855-.313 4 4 0 0 0-.98-.113 4 4 0 0 0-1.646.34 4.3 4.3 0 0 0-1.344.928q-.577.588-.918 1.366a4 4 0 0 0-.338 1.629M94.975 24h-3.669l1.785-4.614-5.528-14.194h3.693l3.644 9.454h.05l3.644-9.454h3.669zm4.88-5.569a.7.7 0 0 0-.517.213.7.7 0 0 0-.213.516q0 .301.213.516a.7.7 0 0 0 .517.213.7.7 0 0 0 .516-.213.7.7 0 0 0 .214-.516q0-.301-.214-.516a.7.7 0 0 0-.516-.213m5.37-.171a1.36 1.36 0 0 1-2.222-.438 1.3 1.3 0 0 1-.108-.535q0-.28.108-.53a1.38 1.38 0 0 1 .729-.726 1.36 1.36 0 0 1 1.493.293l.054.053.94-.92-.055-.055a2.7 2.7 0 0 0-.861-.58 2.7 2.7 0 0 0-1.044-.205c-.381 0-.745.067-1.081.201a2.7 2.7 0 0 0-.878.559 2.7 2.7 0 0 0-.589.845c-.145.324-.22.683-.22 1.065s.073.741.22 1.065a2.65 2.65 0 0 0 1.467 1.412c.336.132.7.2 1.081.2.37 0 .722-.07 1.045-.21q.484-.208.86-.575l.056-.054-.941-.92zm6.088-2.883a2.7 2.7 0 0 0-.878-.559c-.335-.132-.7-.2-1.081-.2s-.743.066-1.076.2a2.7 2.7 0 0 0-.873.559 2.7 2.7 0 0 0-.59.845 2.6 2.6 0 0 0-.219 1.065c0 .382.073.741.219 1.065a2.65 2.65 0 0 0 1.463 1.412c.333.132.695.2 1.076.2s.746-.066 1.081-.2q.507-.202.878-.563a2.8 2.8 0 0 0 .59-.849q.217-.49.219-1.065c0-.382-.073-.74-.219-1.065a2.7 2.7 0 0 0-.59-.846m-2.485 3.176a1.4 1.4 0 0 1-.725-.732 1.3 1.3 0 0 1-.108-.534q0-.28.108-.53a1.38 1.38 0 0 1 .725-.726q.244-.108.526-.108.29 0 .538.108a1.37 1.37 0 0 1 .837 1.256q0 .282-.108.534a1.37 1.37 0 0 1-.729.732 1.37 1.37 0 0 1-1.064 0m11.032-2.676a1.88 1.88 0 0 0-1.009-1.098 1.9 1.9 0 0 0-.779-.16c-.315 0-.602.09-.852.272a3 3 0 0 0-.553.52 2.3 2.3 0 0 0-.515-.537c-.23-.17-.519-.256-.857-.256q-.251 0-.463.076-.21.075-.375.197a2 2 0 0 0-.393.386v-.516h-1.249v5.058h1.342V16.71a.8.8 0 0 1 .231-.558.8.8 0 0 1 .253-.168.8.8 0 0 1 .305-.062q.334 0 .562.228a.75.75 0 0 1 .229.56v3.11h1.332v-3.11q0-.162.063-.309a.7.7 0 0 1 .169-.25.8.8 0 0 1 .869-.167.9.9 0 0 1 .257.168.8.8 0 0 1 .232.558v3.11H120v-3.11c0-.298-.047-.58-.14-.833">
                          </path>
                        </clipPath>
                        <g clip-path="url(#logo-clip)">
                          <rect width="100%" height="100%">
                          </rect>
                          <circle class="origin-center scale-0 opacity-0 transition group-hover:animate-circle-wave" r="60" cx="60" cy="12" fill="#69B1FF">
                          </circle>
                        </g>
                      </img>
                    </a>
                    <!--$-->
                    <div class="max-lg:hidden">
                      <ul class="relative flex items-center gap-3">
                  <?php foreach ($duogxaolin->getList("SELECT * FROM `navbar`", []) as $navs) {
                    if ($navs['type'] == 0) {?>
                     <li class="flex items-center gap-[4px] text-nowrap rounded-[100px] px-[12px] py-[9px] pb-[7px] text-base
                      leading-none text-[#FFFFFF99] transition-all duration-300
                      hover:bg-[#FFFFFF1F]
                      <?php echo(home_uri() == $navs['slug'] ? ' bg-[#FFFFFF1F]  active' : '') ?>" >
                      <a class="hover:text-[#FFFFFFCC]" href="<?php echo $navs['slug'] ?>" 
                      target="<?php echo $navs['target'] ?>"><?php echo $navs['title'] ?>
                      </a>
                      </li>
                    <?php } else if ($navs['type'] == 1) {?>
                         <li x-data="dropdown" @click.outside="open = false" class="dropdown relative">
                           <div class="flex items-center gap-[4px] text-nowrap rounded-[100px] px-[12px] py-[9px] pb-[7px] 
                           text-base leading-none text-[#FFFFFF99] transition-all duration-300 hover:bg-[#FFFFFF1F] cursor-pointer"
                          @click="toggle()">
                          <a class="hover:text-[#FFFFFFCC]" href="javascript:void(0)"><?php echo $navs['title'] ?></a>
                          <span class="peer inline-flex h-[16px] w-[16px] cursor-pointer items-center justify-center rounded-full 
                          text-[14px] transition-all duration-300 hover:bg-[#4B7DFF]">
                            <span class="inline-flex items-center text-center align-[-.125em]">
                              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" 
                              :class="open ? 'rotate-180' : ''" class="transition-transform duration-300">
                                <path d="m6 9 6 6 6-6" stroke="currentColor" stroke-width="1.5">
                                </path>
                              </svg>
                            </span>
                          </span>
                        </div>
                        <!-- Dropdown Menu -->
                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-200"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="absolute top-full left-0 mt-2 min-w-max bg-[#0E0A2F] border border-[#5081ff33] rounded-[16px] shadow-lg py-2 z-[999999] whitespace-nowrap"
                             x-cloak>
                                 <?php foreach ($duogxaolin->getList("SELECT * FROM `navbar` WHERE `type` != 0 AND `link` = '" . $navs['id'] . "'") as $navss) {?>
                                  <a class="block px-4 py-2 text-[#FFFFFF99] hover:text-[#FFFFFFCC] hover:bg-[#FFFFFF1F] transition-all duration-300" href="<?php echo $navss['slug'] ?>" target="<?php echo $navss['target'] ?>"><?php echo $navss['title'] ?></a>
                                    <?php }?>
                        </div>
                        </li>
                        <?php } }?>
                      </ul>
                    </div>
                    <!--/$-->
                  </div>
                  <div class="flex items-center gap-3">
                    <button @click="sidebar = !sidebar" class="cursor-pointer p-2 lg:hidden">
                      <span class="inline-flex items-center text-center align-[-.125em] block text-[24px]">
                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none">
                           <path d="M4 5a1 1 0 1 0 2 0 1 1 0 0 0-2 0Zm7 0a1 1 0 1 0 2 0 1 1 0 0 0-2 0Zm7 0a1 1 0 1 0 2 0 1 1 0 0 0-2 
                           0ZM4 12a1 1 0 1 0 2 0 1 1 0 0 0-2 0Zm7 0a1 1 0 1 0 2 0 1 1 0 0 0-2 0Zm7 0a1 1 0 1 0 2 0 1 1 0 0 0-2 0ZM4 
                           19a1 1 0 1 0 2 0 1 1 0 0 0-2 0Zm7 0a1 1 0 1 0 2 0 1 1 0 0 0-2 0Zm7 0a1 1 0 1 0 2 0 1 1 0 0 0-2 0Z" 
                           fill="currentColor" stroke="currentColor" stroke-width="1.5">
                          </path>
                        </svg>
                      </span>
                    </button>
                    <!--$-->
                    <div x-data="dropdown" @click.outside="open = false" class="relative cursor-pointer z-50 max-lg:hidden">
                      <div @click="toggle()">
                        <div class="relative flex items-center justify-center overflow-hidden bg-gray-200 rounded-full transition-all duration-300 hover:ring-2 hover:ring-[#4B7DFF] hover:ring-opacity-50" style="width:32px;height:32px;background-color:#711CE4">
                          <img alt="Nguyễn Thái Dương" loading="lazy" decoding="async" data-nimg="fill" class="object-cover" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="maxWidth: 100vw" srcset="https://cdn.muakey.com/image/rs:auto:640/q:75/src/https://graph.facebook.com/v3.3/1251872326661668/picture?type=normal@webp 640w, https://cdn.muakey.com/image/rs:auto:750/q:75/src/https://graph.facebook.com/v3.3/1251872326661668/picture?type=normal@webp 750w, https://cdn.muakey.com/image/rs:auto:828/q:75/src/https://graph.facebook.com/v3.3/1251872326661668/picture?type=normal@webp 828w, https://cdn.muakey.com/image/rs:auto:1080/q:75/src/https://graph.facebook.com/v3.3/1251872326661668/picture?type=normal@webp 1080w, https://cdn.muakey.com/image/rs:auto:1200/q:75/src/https://graph.facebook.com/v3.3/1251872326661668/picture?type=normal@webp 1200w, https://cdn.muakey.com/image/rs:auto:1920/q:75/src/https://graph.facebook.com/v3.3/1251872326661668/picture?type=normal@webp 1920w, https://cdn.muakey.com/image/rs:auto:2048/q:75/src/https://graph.facebook.com/v3.3/1251872326661668/picture?type=normal@webp 2048w, https://cdn.muakey.com/image/rs:auto:3840/q:75/src/https://graph.facebook.com/v3.3/1251872326661668/picture?type=normal@webp 3840w" src="https://cdn.muakey.com/image/rs:auto:3840/q:75/src/https://graph.facebook.com/v3.3/1251872326661668/picture?type=normal@webp">
                        </div>
                      </div>

                      <!-- Modal Overlay Backdrop -->
                      <div x-show="open"
                           x-transition:enter="transition ease-out duration-300"
                           x-transition:enter-start="opacity-0"
                           x-transition:enter-end="opacity-100"
                           x-transition:leave="transition ease-in duration-200"
                           x-transition:leave-start="opacity-100"
                           x-transition:leave-end="opacity-0"
                           @click="open = false"
                           class="fixed bottom-0 left-0 right-0 top-0 z-[999997] bg-[rgba(0,0,0,0.5)]"
                           x-cloak>
                      </div>
                      <!-- User Profile Dropdown with Modal Effect -->
                      <div x-show="open"
                           x-transition:enter="transition ease-out duration-300"
                           x-transition:enter-start="opacity-0 transform scale-95 translate-y-[-10px]"
                           x-transition:enter-end="opacity-100 transform scale-100 translate-y-0"
                           x-transition:leave="transition ease-in duration-200"
                           x-transition:leave-start="opacity-100 transform scale-100 translate-y-0"
                           x-transition:leave-end="opacity-0 transform scale-95 translate-y-[-10px]"
                           class="absolute z-[999999] right-0 top-full"
                           x-cloak
                           @click.stop>
                        <div>
                          <div class="mt-[4px] w-[371px] cursor-auto rounded-[24px] border border-[#5081ff33] bg-[#0E0A2F] p-[24px] leading-none text-[#fff] shadow-2xl">
                            <div class="flex items-center justify-between">
                              <div class="flex items-center gap-[4px]">
                                <span class="text-[14px]">Số dư</span>
                                <span class="inline-block" style="width:24px;height:24px">
                                  <div class="lf-player-container">
                                    <div id="lottie" style="background: transparent; margin: 0px auto; outline: none; overflow: hidden; width: 24px; height: 24px; transform: scale(1.5);">

                                    </div>
                                  </div>
                                </span>
                                <span class="text-[14px] font-[400] text-[#FFCA06]">0đ</span>
                              </div>
                              <a class="rounded-full bg-[#4B7DFF] px-[24px] pb-[6px] pt-[8px] text-[14px] font-[400]" href="/recharge">Nạp tiền</a>
                            </div>
                            <!--$-->
                            <!--/$-->
                            <ul class="grid  grid-cols-2 gap-[24px] mt-[24px]">
                              <li>
                                <a class="flex cursor-pointer items-center gap-[8px] leading-none" href="/account">
                                  <span class="text-[24px]">
                                    <span class="inline-flex items-center text-center align-[-.125em]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none">
                                        <path d="M3 12a9 9 0 1 0 18.001 0A9 9 0 0 0 3 12Z" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                        <path d="M9 10a3 3 0 1 0 6 0 3 3 0 0 0-6 0Zm-2.832 8.849A4 4 0 0 1 10 16h4a4 4 0 0 1 3.834 2.855" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                      </svg>
                                    </span>
                                  </span>
                                  <span class="line-clamp-1 text-[14px]">Tài khoản của tôi</span>
                                </a>
                              </li>
                              <li>
                                <a class="flex cursor-pointer items-center gap-[8px] leading-none" href="/recharge">
                                  <span class="text-[24px]">
                                    <span class="inline-flex items-center text-center align-[-.125em]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 25 24" fill="none">
                                        <path d="M15.32 9.333a2 2 0 0 0-.626-.942A1.7 1.7 0 0 0 13.7 8h-2.4c-.477 0-.935.21-1.273.586A2.12 2.12 0 0 0 9.5 10c0 .53.19 1.04.527 1.414.338.375.796.586 1.273.586h2.4c.477 0 .935.21 1.273.586.337.375.527.884.527 1.414s-.19 1.04-.527 1.414A1.7 1.7 0 0 1 13.7 16h-2.4a1.7 1.7 0 0 1-.994-.391 2 2 0 0 1-.626-.942M12.5 6v2m0 8v2" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                        <path d="M3.5 12a9 9 0 1 0 18.001 0A9 9 0 0 0 3.5 12Z" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                      </svg>
                                    </span>
                                  </span>
                                  <span class="line-clamp-1 text-[14px]">Nạp tiền</span>
                                </a>
                              </li>
                              <li>
                                <a class="flex cursor-pointer items-center gap-[8px] leading-none" href="/account/inventory">
                                  <span class="text-[24px]">
                                    <span class="inline-flex items-center text-center align-[-.125em]">
                                      <svg width="1em" height="1em" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M2 9.75A.75.75 0 0 1 2.75 9h18a.75.75 0 0 1 .75.75v8a2.75 2.75 0 0 1-2.75 2.75h-14A2.75 2.75 0 0 1 2 17.75zm1.5.75v7.25c0 .69.56 1.25 1.25 1.25h14c.69 0 1.25-.56 1.25-1.25V10.5z" fill="currentColor">
                                        </path>
                                        <path d="M2 7.75A3.75 3.75 0 0 1 5.75 4h12a3.75 3.75 0 0 1 3.75 3.75v2a.75.75 0 0 1-.75.75h-18A.75.75 0 0 1 2 9.75zM5.75 5.5A2.25 2.25 0 0 0 3.5 7.75V9H20V7.75a2.25 2.25 0 0 0-2.25-2.25z" fill="currentColor">
                                        </path>
                                        <path d="M13 7.75A3.75 3.75 0 0 1 16.75 4h1a3.75 3.75 0 0 1 3.75 3.75v2a.75.75 0 0 1-.75.75h-7a.75.75 0 0 1-.75-.75zm3.75-2.25a2.25 2.25 0 0 0-2.25 2.25V9H20V7.75a2.25 2.25 0 0 0-2.25-2.25z" fill="currentColor">
                                        </path>
                                        <path d="M13 9.75a.75.75 0 0 1 .75-.75h7a.75.75 0 0 1 .75.75v8a2.75 2.75 0 0 1-2.75 2.75h-3A2.75 2.75 0 0 1 13 17.75zm1.5.75v7.25c0 .69.56 1.25 1.25 1.25h3c.69 0 1.25-.56 1.25-1.25V10.5zm-8-.75A.75.75 0 0 1 7.25 9h1.8a.75.75 0 0 1 .75.75v1.8a1.65 1.65 0 1 1-3.3 0zm1.5.75v1.05a.15.15 0 0 0 .3 0V10.5z" fill="currentColor">
                                        </path>
                                      </svg>
                                    </span>
                                  </span>
                                  <span class="line-clamp-1 text-[14px]">Kho hàng</span>
                                </a>
                              </li>
                              <li>
                                <a class="flex cursor-pointer items-center gap-[8px] leading-none" href="/account/reward">
                                  <span class="text-[24px]">
                                    <span class="inline-flex items-center text-center align-[-.125em]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none">
                                        <path d="m12.164 22.869 8.464-4.395c.188-.099.346-.246.455-.426s.168-.387.167-.597V6.549c0-.21-.057-.417-.166-.597a1.17 1.17 0 0 0-.456-.427l-8.464-4.393a1.17 1.17 0 0 0-1.078 0L2.622 5.526a1.17 1.17 0 0 0-.455.426c-.11.18-.168.387-.167.597v10.902a1.15 1.15 0 0 0 .622 1.024l8.464 4.393a1.17 1.17 0 0 0 1.078 0">
                                        </path>
                                        <path d="m12.164 22.869 8.464-4.395c.188-.099.346-.246.455-.426s.168-.387.167-.597V6.549c0-.21-.057-.417-.166-.597a1.17 1.17 0 0 0-.456-.427l-8.464-4.393a1.17 1.17 0 0 0-1.078 0L2.622 5.526a1.17 1.17 0 0 0-.455.426c-.11.18-.168.387-.167.597v10.902a1.15 1.15 0 0 0 .622 1.024l8.464 4.393a1.17 1.17 0 0 0 1.078 0Z" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                        <path d="M17.248 11.37a.23.23 0 0 0-.077-.152.24.24 0 0 0-.162-.061h-4.99A2.93 2.93 0 0 0 9.184 9C7.566 9 6.25 10.293 6.25 11.884s1.316 2.883 2.934 2.883c1.447 0 2.649-1.035 2.887-2.391h1.112v1.556h.002a.23.23 0 0 0 .075.155c.044.04.103.063.163.063h.78a.24.24 0 0 0 .164-.063.23.23 0 0 0 .075-.155h.002v-1.556h.63v2.405h.002c.004.06.031.115.075.156s.103.063.163.063h.778c.06 0 .119-.023.163-.063a.23.23 0 0 0 .076-.156h.002v-2.405h.677a.24.24 0 0 0 .17-.07.24.24 0 0 0 .07-.167v-.77zm-8.064 2.188c-.928 0-1.683-.743-1.683-1.655s.755-1.654 1.683-1.654c.927 0 1.682.742 1.682 1.654s-.755 1.654-1.682 1.654" fill="currentColor">
                                        </path>
                                        <path d="M17.248 11.37a.23.23 0 0 0-.077-.152.24.24 0 0 0-.162-.061h-4.99A2.93 2.93 0 0 0 9.184 9C7.566 9 6.25 10.293 6.25 11.884s1.316 2.883 2.934 2.883c1.447 0 2.649-1.035 2.887-2.391h1.112v1.556h.002a.23.23 0 0 0 .075.155c.044.04.103.063.163.063h.78a.24.24 0 0 0 .164-.063.23.23 0 0 0 .075-.155h.002v-1.556h.63v2.405h.002c.004.06.031.115.075.156s.103.063.163.063h.778c.06 0 .119-.023.163-.063a.23.23 0 0 0 .076-.156h.002v-2.405h.677a.24.24 0 0 0 .17-.07.24.24 0 0 0 .07-.167v-.77h-.003Zm-8.064 2.188c-.928 0-1.683-.743-1.683-1.655s.755-1.654 1.683-1.654c.927 0 1.682.742 1.682 1.654s-.755 1.654-1.682 1.654Z" stroke="currentColor" stroke-width="0.1">
                                        </path>
                                      </svg>
                                    </span>
                                  </span>
                                  <span class="line-clamp-1 text-[14px]">Đổi điểm</span>
                                </a>
                              </li>
                              <li>
                                <a class="flex cursor-pointer items-center gap-[8px] leading-none" href="/account/transactions">
                                  <span class="text-[24px]">
                                    <span class="inline-flex items-center text-center align-[-.125em]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="none">
                                        <path d="M13 17a4 4 0 1 0 8 0 4 4 0 0 0-8 0Z" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                        <path d="M17 13v4h4M12 3v4a1 1 0 0 0 1 1h4" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                        <path d="M11.5 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v2m0 3v4" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                      </svg>
                                    </span>
                                  </span>
                                  <span class="line-clamp-1 text-[14px]">Lịch sử giao dịch</span>
                                </a>
                              </li>
                              <li>
                                <a class="flex cursor-pointer items-center gap-[8px] leading-none" href="/account/vouchers">
                                  <span class="text-[24px]">
                                    <span class="inline-flex items-center text-center align-[-.125em]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="none">
                                        <path d="M3.5 9a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-16a1 1 0 0 1-1-1zm9-1v13" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                        <path d="M19.5 12v7a2 2 0 0 1-2 2h-10a2 2 0 0 1-2-2v-7M8 8a2.5 2.5 0 1 1 0-5c.965-.016 1.91.452 2.713 1.344.802.891 1.425 2.166 1.787 3.656.362-1.49.985-2.764 1.787-3.656C15.09 3.452 16.035 2.984 17 3a2.5 2.5 0 0 1 0 5" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                      </svg>
                                    </span>
                                  </span>
                                  <span class="line-clamp-1 text-[14px]">Kho Voucher</span>
                                </a>
                              </li>
                              <li>
                                <a class="flex cursor-pointer items-center gap-[8px] leading-none" href="/account/wishlists">
                                  <span class="text-[24px]">
                                    <span class="inline-flex items-center text-center align-[-.125em]">
                                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1em" height="1em" fill="none">
                                        <path d="M19.501 12.572 12.001 20l-7.5-7.428a5 5 0 1 1 7.5-6.566 5 5 0 1 1 7.5 6.572" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                      </svg>
                                    </span>
                                  </span>
                                  <span class="line-clamp-1 text-[14px]">Yêu thích</span>
                                </a>
                              </li>
                              <li>
                                <button 
                                onclick="logout()"
                                class="flex cursor-pointer items-center gap-[8px] leading-none">
                                  <span class="text-[24px]">
                                    <span class="inline-flex items-center text-center align-[-.125em]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="none">
                                        <path d="M10.5 8V6a2 2 0 0 1 2-2h7a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2v-2" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                        <path d="M15.5 12h-12l3-3m0 6-3-3" stroke="currentColor" stroke-width="1.5">
                                        </path>
                                      </svg>
                                    </span>
                                  </span>
                                  <span class="line-clamp-1 text-[14px]">Đăng xuất</span>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!--/$-->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Mobile Sidebar Overlay -->
    <div x-show="sidebar"
         class="fixed inset-0 z-[1000000] bg-black/80 transition-opacity duration-300 ease-in-out lg:hidden"
         style="display: none;"
         @click="sidebar = false"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"></div>

    <!-- Mobile Sidebar Panel -->
    <div x-show="sidebar"
         class="fixed left-0 top-0 h-full w-[85%] max-w-sm bg-[#0E0A2F] border-r border-[#5081ff33] shadow-lg p-6 z-[1000001] lg:hidden"
         style="display: none;"
         x-transition:enter="transform transition-transform duration-500 ease-in-out"
         x-transition:enter-start="-translate-x-full opacity-0"
         x-transition:enter-end="translate-x-0 opacity-100"
         x-transition:leave="transform transition-transform duration-500 ease-in-out"
         x-transition:leave-start="translate-x-0 opacity-100"
         x-transition:leave-end="-translate-x-full opacity-0">

        <!-- Close Button -->
        <button @click="sidebar = false" class="absolute right-4 top-4 text-[#FFFFFF99] hover:text-[#FFFFFFCC] transition-colors duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12"></path>
            </svg>
        </button>

        <!-- Sidebar Content -->
        <div class="mt-8">
            <!-- Logo -->
            <div class="text-center mb-8">
                <a href="/" class="inline-block">
                    <img src="<?php echo $duogxaolin->site('logo') ?>" alt="<?php echo $duogxaolin->site('title') ?>" class="logo h-6 w-auto object-contain transition-all duration-300 hover:scale-105 hover:brightness-110">
                </a>
            </div>

            <!-- Navigation Menu -->
            <nav class="flex flex-col space-y-2">
                <?php foreach ($duogxaolin->getList("SELECT * FROM `navbar`", []) as $navs) {
                    if ($navs['type'] == 0) {?>
                        <a class="flex items-center gap-[4px] text-nowrap rounded-[12px] px-[16px] py-[12px] text-base leading-none text-[#FFFFFF99] transition-all duration-300 hover:bg-[#FFFFFF1F] hover:text-[#FFFFFFCC]<?php echo(home_uri() == $navs['slug'] ? ' bg-[#FFFFFF1F] text-[#FFFFFFCC] active' : '') ?>"
                           href="<?php echo $navs['slug'] ?>"
                           target="<?php echo $navs['target'] ?>"
                           @click="sidebar = false">
                            <?php echo $navs['title'] ?>
                        </a>
                    <?php } else if ($navs['type'] == 1) {?>
                        <div x-data="dropdown" @click.outside="open = false" class="dropdown">
                            <div class="flex items-center gap-[4px] text-nowrap rounded-[12px] px-[16px] py-[12px] text-base leading-none text-[#FFFFFF99] transition-all duration-300 hover:bg-[#FFFFFF1F] hover:text-[#FFFFFFCC] cursor-pointer" @click="toggle()">
                                <span class="flex-1"><?php echo $navs['title'] ?></span>
                                <span class="inline-flex h-[16px] w-[16px] cursor-pointer items-center justify-center rounded-full text-[14px] transition-all duration-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none" :class="open ? 'rotate-180' : ''" class="transition-transform duration-300">
                                        <path d="m6 9 6 6 6-6" stroke="currentColor" stroke-width="1.5"></path>
                                    </svg>
                                </span>
                            </div>
                            <!-- Dropdown Menu -->
                            <div x-show="open"
                                 x-transition:enter="transition ease-out duration-300"
                                 x-transition:enter-start="opacity-0 transform scale-95"
                                 x-transition:enter-end="opacity-100 transform scale-100"
                                 x-transition:leave="transition ease-in duration-200"
                                 x-transition:leave-start="opacity-100 transform scale-100"
                                 x-transition:leave-end="opacity-0 transform scale-95"
                                 class="ml-4 mt-2 space-y-1"
                                 x-cloak>
                                <?php foreach ($duogxaolin->getList("SELECT * FROM `navbar` WHERE `type` != 0 AND `link` = '" . $navs['id'] . "'") as $navss) {?>
                                    <a class="block px-4 py-2 text-[#FFFFFF99] hover:text-[#FFFFFFCC] hover:bg-[#FFFFFF1F] rounded-[8px] transition-all duration-300"
                                       href="<?php echo $navss['slug'] ?>"
                                       target="<?php echo $navss['target'] ?>"
                                       @click="sidebar = false">
                                        <?php echo $navss['title'] ?>
                                    </a>
                                <?php }?>
                            </div>
                        </div>
                    <?php }
                    }?>
            </nav>

            <!-- Additional Links -->
            <div class="mt-8 pt-6 border-t border-[#5081ff33]">
                <div class="space-y-2">
                    <a class="flex items-center gap-[4px] text-nowrap rounded-[12px] px-[16px] py-[12px] text-base leading-none text-[#FFFFFF99] transition-all duration-300 hover:bg-[#FFFFFF1F] hover:text-[#FFFFFFCC]"
                       href="https://muakey.com/news"
                       @click="sidebar = false">Game news</a>
                    <a class="flex items-center gap-[4px] text-nowrap rounded-[12px] px-[16px] py-[12px] text-base leading-none text-[#FFFFFF99] transition-all duration-300 hover:bg-[#FFFFFF1F] hover:text-[#FFFFFFCC]"
                       href="https://muakey.com/recruitment"
                       @click="sidebar = false">Tuyển dụng</a>
                    <a class="flex items-center gap-[4px] text-nowrap rounded-[12px] px-[16px] py-[12px] text-base leading-none text-[#FFFFFF99] transition-all duration-300 hover:bg-[#FFFFFF1F] hover:text-[#FFFFFFCC]"
                       href="https://huong-dan-muakey.notion.site/huong-dan"
                       @click="sidebar = false">Hướng dẫn Muakey</a>
                </div>
            </div>
        </div>
    </div>

<?php if ($duogxaolin->site('nav_mobile') == 'ON'): ?>
<!-- Modern Mobile Bottom Navigation Bar with Floating Home Button -->
<div class="mobile-nav-container fixed bottom-0 w-full h-[64px] z-[999998] lg:hidden">
    <!-- Left Section -->
    <div class="nav-section-left">
        <!-- Danh Mục (Sidebar Toggle) -->
        <div @click="sidebar = !sidebar" class="nav-item">
            <div class="nav-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 25 24" fill="none">
                    <path d="M4 6h16M4 12h16M4 18h16" stroke="currentColor" stroke-width="1.5"></path>
                </svg>
            </div>
            <span class="nav-label">Danh Mục</span>
        </div>

        <!-- Nạp Tiền -->
        <a href="/recharge" class="nav-item">
            <div class="nav-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 25 24" fill="none">
                    <path d="M15.32 9.333a2 2 0 0 0-.626-.942A1.7 1.7 0 0 0 13.7 8h-2.4c-.477 0-.935.21-1.273.586A2.12 2.12 0 0 0 9.5 10c0 .53.19 1.04.527 1.414.338.375.796.586 1.273.586h2.4c.477 0 .935.21 1.273.586.337.375.527.884.527 1.414s-.19 1.04-.527 1.414A1.7 1.7 0 0 1 13.7 16h-2.4a1.7 1.7 0 0 1-.994-.391 2 2 0 0 1-.626-.942M12.5 6v2m0 8v2" stroke="currentColor" stroke-width="1.5"></path>
                    <path d="M3.5 12a9 9 0 1 0 18.001 0A9 9 0 0 0 3.5 12Z" stroke="currentColor" stroke-width="1.5"></path>
                </svg>
            </div>
            <span class="nav-label">Nạp Tiền</span>
        </a>
    </div>

    <!-- Floating Home Button -->
    <a href="/" class="floating-home-button">
        <div class="home-button-inner">
            <svg xmlns="http://www.w3.org/2000/svg" width="1.2em" height="1.2em" viewBox="0 0 24 24" fill="none">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" stroke-width="1.5"></path>
                <path d="M9 22V12h6v10" stroke="currentColor" stroke-width="1.5"></path>
            </svg>
        </div>
    </a>

    <!-- Right Section -->
    <div class="nav-section-right">
        <!-- Lịch Sử -->
        <a href="/history" class="nav-item">
            <div class="nav-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none">
                    <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" stroke="currentColor" stroke-width="1.5"></path>
                    <path d="M3 3v5h5M12 7v5l4 2" stroke="currentColor" stroke-width="1.5"></path>
                </svg>
            </div>
            <span class="nav-label">Lịch Sử</span>
        </a>

        <!-- Tài Khoản -->
        <a href="/customer/profile" class="nav-item">
            <div class="nav-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none">
                    <path d="M3 12a9 9 0 1 0 18.001 0A9 9 0 0 0 3 12Z" stroke="currentColor" stroke-width="1.5"></path>
                    <path d="M9 10a3 3 0 1 0 6 0 3 3 0 0 0-6 0Zm-2.832 8.849A4 4 0 0 1 10 16h4a4 4 0 0 1 3.834 2.855" stroke="currentColor" stroke-width="1.5"></path>
                </svg>
            </div>
            <span class="nav-label">Tài Khoản</span>
        </a>
    </div>
</div>

<!-- Modern Dark Blue-Green Themed Floating Navigation Styles -->
<style>
.mobile-nav-container {
    position: fixed;
    width: 100%;
    height: 64px;
    bottom: 0;
    left: 0;
    z-index: 999998;
}

.nav-section-left {
    float: left;
    width: calc(50% - 40px);
    height: 64px;
    background: #13112E;
    border-top: 1px solid #5081FF33;
    border-top-right-radius: 20px;
    box-shadow: 0 -4px 8px 0 rgba(80, 129, 255, 0.15);
    display: flex;
}

.nav-section-right {
    float: right;
    width: calc(50% - 40px);
    height: 64px;
    background: #13112E;
    border-top: 1px solid #5081FF33;
    border-top-left-radius: 20px;
    box-shadow: 0 -4px 8px 0 rgba(80, 129, 255, 0.15);
    display: flex;
}

.nav-item {
    width: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding-top: 8px;
    color: #9F9BAB;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-item:hover {
    color: #FFFFFFCC;
    transform: translateY(-1px);
}

.nav-icon {
    font-size: 26px;
    height: 26px;
    display: block;
    margin: 0 auto 6px;
    width: 26px;
    transition: transform 0.3s ease;
}

.nav-item:hover .nav-icon {
    transform: scale(1.1);
}

.nav-label {
    display: block;
    height: 16px;
    line-height: 16px;
    margin-top: 2px;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.floating-home-button {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    background: transparent;
    border-radius: 50%;
    top: -26px;
    box-sizing: border-box;
    padding: 5px;
    z-index: 999999;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.floating-home-button:hover {
    transform: translateX(-50%) scale(1.05);
}

.floating-home-button::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: #13112E;
    border-radius: 50%;
    box-shadow: 0 33px 0 10px #13112E;
    z-index: -1;
}

.home-button-inner {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #4B7DFF 0%, #5081FF 100%);
    border-radius: 50%;
    box-sizing: border-box;
    text-align: center;
    cursor: pointer;
    color: #fff;
    font-size: 24px;
    box-shadow:
        0 8px 16px rgba(75, 125, 255, 0.3),
        inset 0 -4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.floating-home-button:hover .home-button-inner {
    background: linear-gradient(135deg, #5081FF 0%, #4B7DFF 100%);
    box-shadow:
        0 12px 24px rgba(75, 125, 255, 0.4),
        inset 0 -4px 8px rgba(0, 0, 0, 0.15);
}

.floating-home-button::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: block;
    border-radius: 50%;
    box-shadow: inset 0 -10px 5px 0 rgba(5, 26, 40, 0.08);
    z-index: 1;
    pointer-events: none;
}

/* Clear floats */
.mobile-nav-container::after {
    content: "";
    display: table;
    clear: both;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .nav-section-left,
    .nav-section-right {
        width: calc(50% - 35px);
    }

    .floating-home-button {
        width: 70px;
        height: 70px;
        top: -23px;
    }

    .nav-icon {
        font-size: 24px;
        height: 24px;
        width: 24px;
        margin: 0 auto 5px;
    }

    .nav-label {
        font-size: 11px;
        height: 15px;
        line-height: 15px;
    }

    .nav-item {
        padding-top: 6px;
    }
}
</style>
<?php endif; ?>
  </div>

