<?php
    require_once $_SERVER['DOCUMENT_ROOT'] . '/config.php';

    $slug = xss($_GET['slug']);
    if (! $slug) {
        require_once $_SERVER['DOCUMENT_ROOT'] . '/404.php';
        die();
    }
    $acc = $duogxaolin->getRow("SELECT * FROM `account` WHERE `slug` = ? AND `type` = ? ",[$slug,'muanick']);
    if (! $acc) {
        require_once $_SERVER['DOCUMENT_ROOT'] . '/404.php';
        die();
    }
    if ($acc['status'] < 1) {
        require_once $_SERVER['DOCUMENT_ROOT'] . '/404.php';
        die();
    }
    $service = $duogxaolin->getRow("SELECT * FROM `category` WHERE `id` = ?", [$acc['category_id']]);
    if (! $service) {
        require_once $_SERVER['DOCUMENT_ROOT'] . '/404.php';
        die();
    }
    $game = $duogxaolin->getRow("SELECT * FROM `game` WHERE `id` = ? ",[ $service['game_id']]);
    if (! $game) {
        require_once $_SERVER['DOCUMENT_ROOT'] . '/404.php';
        die();
    }
    if ($service['type'] != 'muanick') {
        require_once $_SERVER['DOCUMENT_ROOT'] . '/404.php';
        die();
    }
    $imgs = $duogxaolin->getList("SELECT * FROM `account_images`
     WHERE `account_slug` = ? ORDER BY COALESCE(`sort_order`, `id`) ASC"
    ,[$slug]
    );

    // FIXED: Don't redirect to 404 if no images, handle gracefully
    // Instead, we'll show a placeholder or default image in the gallery
    require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/header.php';
    require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/navbar.php';
?>
<style>
  

/* ===== SWIPER CUSTOMIZATIONS - FIXED FOR INFINITE STRETCHING & ENHANCED TOUCH ===== */
.swiper {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
}

.swiper-wrapper {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
}

.swiper-slide {
  min-height: 0;
  height: auto;
  /* FIXED: Prevent excessive slide widths */
  width: 100% !important;
  max-width: 100% !important;
}

.swiper-slide img {
  display: block;
  width: 100% !important;
  height: auto;
  max-height: 80vh;
  max-width: 100% !important;
}

</style>
<!-- Gaming Account Page Container -->
<div class="gaming-account-container">
  <div class="container mx-auto px-4 py-8 relative z-2">

    <!-- Page Header -->
    <div class="text-center mb-8">
      <h1 class="gaming-title text-4xl md:text-5xl font-bold mb-4">
        Tài Khoản <span class="gaming-accent-text">#<?php echo $slug ?></span>
      </h1>
      <p class="text-gray-300 text-lg"><?php echo $acc['title'] ?></p>
    </div>

    <!-- ENHANCED: Gaming Inventory Layout - Sidebar + Main Area -->
    <div class="flex flex-col lg:flex-row gap-6 mb-8">

      <!-- ENHANCED: Gaming Sidebar - Account Info + Thumbnails -->
      <div class="w-full lg:w-80 xl:w-96 order-2 lg:order-1 space-y-6">

        <!-- Account Information Card -->
        <div class="gaming-account-card p-6">

          <!-- Account Details -->
          <div class="mb-6">
            <h2 class="text-xl font-bold text-white mb-4 flex items-center">
              <svg class="w-5 h-5 mr-2 text-gaming-blue" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              Thông Tin Tài Khoản
            </h2>

            <div class="space-y-3">
              <?php if ($game['server'] != '' && $acc['server'] != ''): ?>
                <div class="gaming-info-card">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300 text-sm font-medium"><?php echo $game['server'] ?></span>
                    <span class="gaming-accent-text font-semibold"><?php echo $acc['server'] ?></span>
                  </div>
                </div>
              <?php endif; ?>

              <?php if ($game['level'] != '' && $acc['level'] != ''): ?>
                <div class="gaming-info-card">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300 text-sm font-medium"><?php echo $game['level'] ?></span>
                    <span class="gaming-accent-text font-semibold"><?php echo $acc['level'] ?></span>
                  </div>
                </div>
              <?php endif; ?>

              <?php if ($game['rank'] != '' && $acc['rank'] != ''): ?>
                <div class="gaming-info-card">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300 text-sm font-medium"><?php echo $game['rank'] ?></span>
                    <span class="gaming-accent-text font-semibold"><?php echo $acc['rank'] ?></span>
                  </div>
                </div>
              <?php endif; ?>

              <?php if ($game['attribute_4'] != '' && $acc['attribute_4'] != ''): ?>
                <div class="gaming-info-card">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300 text-sm font-medium"><?php echo $game['attribute_4'] ?></span>
                    <span class="gaming-accent-text font-semibold"><?php echo $acc['attribute_4'] ?></span>
                  </div>
                </div>
              <?php endif; ?>

              <?php if ($game['attribute_5'] != '' && $acc['attribute_5'] != ''): ?>
                <div class="gaming-info-card">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300 text-sm font-medium"><?php echo $game['attribute_5'] ?></span>
                    <span class="gaming-accent-text font-semibold"><?php echo $acc['attribute_5'] ?></span>
                  </div>
                </div>
              <?php endif; ?>
            </div>
          </div>

          <!-- Account Note -->
          <?php if (!empty($acc['note'])): ?>
            <div class="mb-6">
              <h3 class="text-lg font-semibold text-white mb-3">Ghi Chú</h3>
              <div class="gaming-info-card">
                <p class="text-gray-300 text-sm leading-relaxed"><?php echo $acc['note'] ?></p>
              </div>
            </div>
          <?php endif; ?>
          <!-- Pricing Section -->
          <?php if ($acc['user_id'] == null && $acc['status'] == 1) {?>
            <div class="space-y-4">

              <!-- Discount Information -->
              <?php if ($acc['discount'] != 0) {?>
                <div class="gaming-info-card">
                  <div class="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div class="text-amber-400 text-sm font-medium mb-1">Giá Gốc</div>
                      <div class="text-amber-400 text-lg font-bold">
                        <?php echo format_cash($acc['money']) ?><sup class="text-xs">đ</sup>
                      </div>
                    </div>
                    <div>
                      <div class="text-emerald-400 text-sm font-medium mb-1">Giảm Giá</div>
                      <div class="text-emerald-400 text-lg font-bold">
                        <?php echo $acc['discount'] ?>%
                      </div>
                    </div>
                  </div>
                </div>
              <?php }?>

              <!-- Payment Methods -->
              <?php if ($duogxaolin->site('card_prices') == 'ON' && intval($duogxaolin->site('card_discount')) > 0): ?>
                <div class="gaming-info-card">
                  <div class="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div class="text-yellow-400 text-sm font-medium mb-1">CARD</div>
                      <div class="text-yellow-400 text-lg font-bold">
                        <?php echo format_cash(($acc['money'] - $acc['money'] * $acc['discount'] / 100) * ((100 + intval($duogxaolin->site('card_discount'))) / 100)) ?>
                        <sup class="text-xs">đ</sup>
                      </div>
                    </div>
                    <div>
                      <div class="text-emerald-400 text-sm font-medium mb-1">ATM</div>
                      <div class="text-emerald-400 text-lg font-bold">
                        <?php echo format_cash($acc['money'] - $acc['money'] * $acc['discount'] / 100) ?>
                        <sup class="text-xs">đ</sup>
                      </div>
                    </div>
                  </div>
                </div>
              <?php else: ?>
                <div class="gaming-info-card text-center">
                  <div class="text-emerald-400 text-sm font-medium mb-2">Tổng Thanh Toán</div>
                  <div class="text-emerald-400 text-3xl font-bold" id="price">
                    <?php echo format_cash($acc['money'] - $acc['money'] * $acc['discount'] / 100) ?>
                    <span class="text-lg"><?php echo $duogxaolin->site('c') ?></span>
                  </div>
                </div>
              <?php endif; ?>

              <!-- FIXED: Purchase Button with Custom Modal Trigger -->
              <button type="button"
                      onclick="openPurchaseModal()"
                      class="gaming-btn-primary w-full">
                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"/>
                </svg>
                Mua Tài Khoản
              </button>

              <!-- Installment Option -->
              <?php if ($duogxaolin->site('installment') == 'ON'): ?>
                <div class="text-center">
                  <div class="text-gray-400 text-sm mb-3">Hoặc</div>
                  <form submit-ajax="duogxaolin" action="/api/service/account" method="POST">
                    <input type="hidden" name="type" value="installment">
                    <input type="hidden" name="id" value="<?php echo $acc['id'] ?>">
                    <input type="hidden" name="code" value="<?php echo $acc['slug'] ?>">
                    <button type="submit"
                            order="Chức năng đang được phát triển, sẽ khả dụng trong thời gian sắp tới"
                            class="gaming-btn-primary w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600">
                      <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                      </svg>
                      Mua Trả Góp
                    </button>
                  </form>
                </div>
              <?php endif; ?>
            </div>
          <!-- Account Status - Sold -->
          <?php } else if ($acc['status'] == 2) {?>
            <div class="space-y-4">
              <?php if ($acc['discount'] != 0) {?>
                <div class="gaming-info-card">
                  <div class="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div class="text-gray-400 text-sm font-medium mb-1">Giá Gốc</div>
                      <div class="text-gray-300 text-lg font-bold">
                        <?php echo format_cash($acc['money']) ?><?php echo $duogxaolin->site('c') ?>
                      </div>
                    </div>
                    <div>
                      <div class="text-gray-400 text-sm font-medium mb-1">Đã Giảm</div>
                      <div class="text-gray-300 text-lg font-bold"><?php echo $acc['discount'] ?>%</div>
                    </div>
                  </div>
                </div>
              <?php }?>

              <div class="gaming-info-card text-center">
                <div class="text-gray-400 text-sm font-medium mb-2">Tổng Đã Thanh Toán</div>
                <div class="text-gray-300 text-2xl font-bold">
                  <?php echo format_cash($acc['money'] - $acc['money'] * $acc['discount'] / 100) ?>
                  <?php echo $duogxaolin->site('c') ?>
                </div>
              </div>

              <div class="gaming-status-sold">
                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                </svg>
                Tài Khoản Đã Được Mua
              </div>
            </div>

          <!-- Account Status - Unavailable -->
          <?php } else {?>
            <div class="gaming-status-sold bg-gradient-to-r from-red-600 to-red-700">
              <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd"/>
              </svg>
              Tài Khoản Không Thể Mua
            </div>
          <?php }?>
        </div>

        <!-- ENHANCED: Sidebar thumbnails removed - now integrated into view controls -->
      </div>

      <!-- ENHANCED: Main Gallery Area - Gaming Grid Layout -->
      <div class="flex-1 order-1 lg:order-2">
        <div class=" <div class="gaming-main-gallery">

          <!-- ENHANCED: Gaming Grid Gallery Header -->
          <div class="gaming-grid-header">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 text-gaming-blue" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
                <h2 class="text-xl font-bold text-white">Hình Ảnh Tài Khoản</h2>
              </div>
              <div class="gaming-grid-stats">
                <span class="text-gaming-blue font-semibold">
                  <?php
                  $imageCount = 0;
                  if (!empty($imgs) && is_array($imgs)) {
                    foreach ($imgs as $img) {
                      if (isset($img['images']) && !empty(trim($img['images']))) {
                        $imageCount++;
                      }
                    }
                  }
                  echo $imageCount . ' ảnh';
                  ?>
                </span>
              </div>
            </div>
          </div>

          <!-- ENHANCED: Gaming Grid Gallery Container -->
          <div class="gaming-grid-container">
            <!-- ENHANCED: Main Featured Image Display -->
            <div class="gaming-featured-display" id="featured-display">
              <div class="swiper mySwiper w-full h-full rounded-lg overflow-hidden">
                <div class="swiper-wrapper">
                <?php
                // ENHANCED: Comprehensive image handling with fallbacks
                if (!empty($imgs) && is_array($imgs)) {
                  echo "<!-- DEBUG: Found " . count($imgs) . " images -->";
                  $hasValidImages = false;
                  $slideIndex = 0; // FIXED: Initialize slide index counter for main gallery

                  foreach ($imgs as $img) {
                    // Validate image data and provide fallback
                    $imagePath = '';
                    if (isset($img['images']) && !empty(trim($img['images']))) {
                      $imagePath = trim($img['images']);
                      $hasValidImages = true;
                    } else {
                      // Use fallback image if no valid path
                      $imagePath = '/assets/image/error_img.png';
                    }

                    echo "<!-- DEBUG: Image path: " . htmlspecialchars($imagePath) . " -->";
                    ?>
                    <div class="swiper-slide"
                         data-image-id="<?php echo htmlspecialchars($img['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                         data-slide-index="<?php echo $slideIndex ?>">
                      <a data-fancybox="gallery" href="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         class="block w-full h-full group cursor-pointer">
                        <!-- ENHANCED: Clean Image Container with Proper Rounded Corners -->
                        <div class="relative w-full h-full overflow-hidden rounded-xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm">
                          <img src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                               alt="Account Image <?php echo htmlspecialchars($img['id'] ?? 'Unknown', ENT_QUOTES, 'UTF-8') ?>"
                               class="w-full h-full object-cover rounded-xl transition-all duration-300 group-hover:scale-105 group-hover:brightness-110"
                               loading="<?php echo $slideIndex === 0 ? 'eager' : 'lazy' ?>"
                               decoding="async"
                               onload="this.style.opacity='1'; this.classList.add('loaded');"
                               onerror="this.src='/assets/image/error_img.png'; this.style.opacity='1';"
                               style="opacity: 0; transition: opacity 0.3s ease-in-out;"
                               data-image-id="<?php echo htmlspecialchars($img['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>">

                          <!-- Enhanced Fallback for broken images -->
                          <div class="absolute inset-0 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl flex items-center justify-center text-gray-400 hidden">
                            <div class="text-center">
                              <svg class="w-16 h-16 mx-auto mb-2 opacity-50" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                              </svg>
                              <p class="text-sm">Không thể tải ảnh</p>
                            </div>
                          </div>

                          <!-- ENHANCED: Elegant Hover Overlay -->
                          <div class="absolute inset-0 bg-gradient-to-t from-blue-500/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center rounded-xl">
                            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform group-hover:scale-110">
                              <div class="bg-white/10 backdrop-blur-md rounded-full p-4 border border-white/20 shadow-lg">
                                <svg class="w-8 h-8 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                </svg>
                              </div>
                            </div>
                          </div>

                          <!-- ENHANCED: Gaming Border Glow Effect -->
                          <div class="absolute inset-0 rounded-xl border-2 border-transparent group-hover:border-blue-400/60 transition-all duration-300 pointer-events-none"></div>

                          <!-- ENHANCED: Gaming Shadow Glow -->
                          <div class="absolute inset-0 rounded-xl shadow-lg shadow-transparent group-hover:shadow-blue-500/25 transition-all duration-300 pointer-events-none"></div>
                        </div>
                      </a>
                    </div>
                  <?php
                    $slideIndex++; // FIXED: Increment slide index counter
                  }

                  // If no valid images found, show placeholder
                  if (!$hasValidImages) {
                    echo "<!-- DEBUG: No valid images found, showing placeholder -->";
                    ?>
                    <div class="swiper-slide">
                      <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-700/80 to-gray-800/90 rounded-xl border border-gray-600/30">
                        <div class="text-center text-gray-300">
                          <div class="w-24 h-24 bg-gray-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-12 h-12 opacity-60" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                            </svg>
                          </div>
                          <p class="text-base font-medium mb-2">Hình ảnh không khả dụng</p>
                          <p class="text-sm opacity-75">Dữ liệu hình ảnh không hợp lệ</p>
                        </div>
                      </div>
                    </div>
                    <?php
                  }
                } else {
                  echo "<!-- DEBUG: No images found in database or invalid data -->";
                  ?>
                  <!-- ENHANCED: No images fallback with improved styling -->
                  <div class="swiper-slide">
                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-700/80 to-gray-800/90 rounded-xl border border-gray-600/30">
                      <div class="text-center text-gray-300">
                        <div class="w-24 h-24 bg-gray-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                          <svg class="w-12 h-12 opacity-60" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                        <p class="text-base font-medium mb-2">Chưa có hình ảnh</p>
                        <p class="text-sm opacity-75">Tài khoản này chưa có hình ảnh minh họa</p>
                      </div>
                    </div>
                  </div>
                <?php } ?>
              </div>

                <!-- ENHANCED: Gaming Navigation Controls -->
                <button class="swiper-button-prev-ex1 absolute left-4 top-1/2 -translate-y-1/2 z-20 w-12 h-12 bg-black bg-opacity-60 hover:bg-opacity-80 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110 border-2 border-white border-opacity-20 hover:border-opacity-40">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                  </svg>
                </button>

                <button class="swiper-button-next-ex1 absolute right-4 top-1/2 -translate-y-1/2 z-20 w-12 h-12 bg-black bg-opacity-60 hover:bg-opacity-80 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110 border-2 border-white border-opacity-20 hover:border-opacity-40">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                  </svg>
                </button>

                <!-- ENHANCED: Gaming Pagination -->
                <div class="swiper-pagination absolute bottom-4 left-1/2 -translate-x-1/2 z-20"></div>
              </div>
            </div>

            <!-- ENHANCED: Gaming Grid Display (Alternative View) -->
            <div class="gaming-grid-display hidden" id="grid-display">
              <div class="gaming-grid-items">
                <?php
                // ENHANCED: Generate grid items for all images
                if (!empty($imgs) && is_array($imgs)) {
                  $gridIndex = 0;
                  foreach ($imgs as $img) {
                    // Validate image data and provide fallback
                    $imagePath = '';
                    $isValidImage = false;

                    if (isset($img['images']) && !empty(trim($img['images']))) {
                      $imagePath = trim($img['images']);
                      $isValidImage = true;
                    } else {
                      $imagePath = '/assets/image/error_img.png';
                    }

                    $activeClass = $gridIndex === 0 ? 'active' : '';
                    ?>
                    <div class="gaming-grid-item <?php echo $activeClass ?>"
                         data-grid-index="<?php echo $gridIndex ?>"
                         data-image-id="<?php echo htmlspecialchars($img['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                         data-image-src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         data-grid-id="grid-item-<?php echo $gridIndex ?>"
                         title="Click to view image <?php echo $gridIndex + 1 ?> (ID: <?php echo $img['id'] ?? 'N/A' ?>): <?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         style="cursor: pointer;">

                      <a data-fancybox="gallery" href="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         class="block w-full h-full group cursor-pointer">
                        <!-- ENHANCED: Gaming Grid Item Container -->
                        <div class="gaming-grid-item-container">
                          <?php if ($isValidImage): ?>
                            <img src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                                 alt="Grid Image <?php echo $gridIndex + 1 ?>"
                                 class="gaming-grid-item-image"
                                 loading="lazy"
                                 decoding="async"
                                 onload="this.style.opacity='1';"
                                 onerror="this.src='/assets/image/error_img.png';"
                                 style="opacity: 0; transition: opacity 0.3s ease;"
                                 data-grid-index="<?php echo $gridIndex ?>">
                          <?php else: ?>
                            <div class="gaming-grid-item-placeholder">
                              <svg fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                              </svg>
                            </div>
                          <?php endif; ?>

                          <!-- ENHANCED: Gaming Hover Effects -->
                          <div class="gaming-grid-item-overlay">
                            <div class="gaming-grid-item-hover-icon">
                              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                    <?php
                    $gridIndex++;
                  }
                } else {
                  // Show placeholder grid item when no images
                  ?>
                  <div class="gaming-grid-item active" data-grid-index="0">
                    <div class="gaming-grid-item-container">
                      <div class="gaming-grid-item-placeholder">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                  <?php
                }
                ?>
              </div>
            </div>

            <!-- ENHANCED: Gaming Thumbnail Display (Third View) -->
            <div class="gaming-thumbnail-display hidden" id="thumbnail-display">
              <div class="gaming-thumbnail-header">
                <div class="gaming-thumbnail-title">
                  <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                  </svg>
                  Xem Nhanh
                </div>
                <div class="gaming-thumbnail-count">
                  <?php
                  $imageCount = 0;
                  if (!empty($imgs) && is_array($imgs)) {
                    foreach ($imgs as $img) {
                      if (isset($img['images']) && !empty(trim($img['images']))) {
                        $imageCount++;
                      }
                    }
                  }
                  echo $imageCount . ' ảnh';
                  ?>
                </div>
              </div>

              <div class="gaming-main-thumbnail-grid" id="main-thumbnail-container">
                <?php
                // ENHANCED: Generate main thumbnails for all images
                if (!empty($imgs) && is_array($imgs)) {
                  $thumbnailIndex = 0;
                  foreach ($imgs as $img) {
                    // Validate image data and provide fallback
                    $imagePath = '';
                    $isValidImage = false;

                    if (isset($img['images']) && !empty(trim($img['images']))) {
                      $imagePath = trim($img['images']);
                      $isValidImage = true;
                    } else {
                      $imagePath = '/assets/image/error_img.png';
                    }

                    $activeClass = $thumbnailIndex === 0 ? 'active' : '';
                    ?>
                    <div class="gaming-main-thumbnail <?php echo $activeClass ?>"
                         data-slide-index="<?php echo $thumbnailIndex ?>"
                         data-image-id="<?php echo htmlspecialchars($img['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                         data-image-src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         data-thumbnail-id="main-thumbnail-<?php echo $thumbnailIndex ?>"
                         title="Click to view image <?php echo $thumbnailIndex + 1 ?> (ID: <?php echo $img['id'] ?? 'N/A' ?>): <?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         style="cursor: pointer;">

                      <a data-fancybox="gallery" href="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         class="block w-full h-full group cursor-pointer">
                        <!-- ENHANCED: Main Thumbnail Container -->
                        <div class="gaming-main-thumbnail-container">
                          <!-- ENHANCED: Always show placeholder first, then overlay image -->
                          <div class="gaming-thumbnail-placeholder">
                            <svg fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                            </svg>
                          </div>

                          <?php if ($isValidImage): ?>
                            <!-- ENHANCED: Improved image loading with better error handling -->
                            <img src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                                 alt="Main Thumbnail <?php echo $thumbnailIndex + 1 ?>"
                                 class="gaming-main-thumbnail-image"
                                 loading="lazy"
                                 decoding="async"
                                 style="opacity: 0; transition: opacity 0.3s ease;"
                                 onload="this.style.opacity='1'; this.style.zIndex='2';"
                                 onerror="this.style.display='none';"
                                 data-thumbnail-index="<?php echo $thumbnailIndex ?>">
                          <?php endif; ?>

                          <!-- ENHANCED: Gaming Hover Effects -->
                          <div class="gaming-main-thumbnail-overlay">
                            <div class="gaming-main-thumbnail-hover-icon">
                              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                    <?php
                    $thumbnailIndex++;
                  }
                } else {
                  // Show placeholder thumbnail when no images
                  ?>
                  <div class="gaming-main-thumbnail active" data-slide-index="0">
                    <div class="gaming-main-thumbnail-container">
                      <div class="gaming-thumbnail-placeholder">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                  <?php
                }
                ?>
              </div>
            </div>

            <!-- ENHANCED: View Toggle Controls with Thumbnail View -->
            <div class="gaming-view-controls">
              <button class="gaming-view-btn active" data-view="featured" title="Featured View">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
              </button>
              <button class="gaming-view-btn" data-view="grid" title="Grid View">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                </svg>
              </button>
              <button class="gaming-view-btn" data-view="thumbnails" title="Thumbnail View">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </button>
            </div>
          </div>


        </div>">

          <!-- ENHANCED: Gaming Grid Gallery Header -->
          <div class="gaming-grid-header">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 text-gaming-blue" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
                <h2 class="text-xl font-bold text-white">Hình Ảnh Tài Khoản</h2>
              </div>
              <div class="gaming-grid-stats">
                <span class="text-gaming-blue font-semibold">
                  <?php
                  $imageCount = 0;
                  if (!empty($imgs) && is_array($imgs)) {
                    foreach ($imgs as $img) {
                      if (isset($img['images']) && !empty(trim($img['images']))) {
                        $imageCount++;
                      }
                    }
                  }
                  echo $imageCount . ' ảnh';
                  ?>
                </span>
              </div>
            </div>
          </div>

          <!-- ENHANCED: Gaming Grid Gallery Container -->
          <div class="gaming-grid-container">
            <!-- ENHANCED: Main Featured Image Display -->
            <div class="gaming-featured-display" id="featured-display">
              <div class="swiper mySwiper w-full h-full rounded-lg overflow-hidden">
                <div class="swiper-wrapper">
                <?php
                // ENHANCED: Comprehensive image handling with fallbacks
                if (!empty($imgs) && is_array($imgs)) {
                  echo "<!-- DEBUG: Found " . count($imgs) . " images -->";
                  $hasValidImages = false;
                  $slideIndex = 0; // FIXED: Initialize slide index counter for main gallery

                  foreach ($imgs as $img) {
                    // Validate image data and provide fallback
                    $imagePath = '';
                    if (isset($img['images']) && !empty(trim($img['images']))) {
                      $imagePath = trim($img['images']);
                      $hasValidImages = true;
                    } else {
                      // Use fallback image if no valid path
                      $imagePath = '/assets/image/error_img.png';
                    }

                    echo "<!-- DEBUG: Image path: " . htmlspecialchars($imagePath) . " -->";
                    ?>
                    <div class="swiper-slide"
                         data-image-id="<?php echo htmlspecialchars($img['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                         data-slide-index="<?php echo $slideIndex ?>">
                      <a data-fancybox="gallery" href="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         class="block w-full h-full group cursor-pointer">
                        <!-- ENHANCED: Clean Image Container with Proper Rounded Corners -->
                        <div class="relative w-full h-full overflow-hidden rounded-xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm">
                          <img src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                               alt="Account Image <?php echo htmlspecialchars($img['id'] ?? 'Unknown', ENT_QUOTES, 'UTF-8') ?>"
                               class="w-full h-full object-cover rounded-xl transition-all duration-300 group-hover:scale-105 group-hover:brightness-110"
                               loading="<?php echo $slideIndex === 0 ? 'eager' : 'lazy' ?>"
                               decoding="async"
                               onload="this.style.opacity='1'; this.classList.add('loaded');"
                               onerror="this.src='/assets/image/error_img.png'; this.style.opacity='1';"
                               style="opacity: 0; transition: opacity 0.3s ease-in-out;"
                               data-image-id="<?php echo htmlspecialchars($img['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>">

                          <!-- Enhanced Fallback for broken images -->
                          <div class="absolute inset-0 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl flex items-center justify-center text-gray-400 hidden">
                            <div class="text-center">
                              <svg class="w-16 h-16 mx-auto mb-2 opacity-50" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                              </svg>
                              <p class="text-sm">Không thể tải ảnh</p>
                            </div>
                          </div>

                          <!-- ENHANCED: Elegant Hover Overlay -->
                          <div class="absolute inset-0 bg-gradient-to-t from-blue-500/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center rounded-xl">
                            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform group-hover:scale-110">
                              <div class="bg-white/10 backdrop-blur-md rounded-full p-4 border border-white/20 shadow-lg">
                                <svg class="w-8 h-8 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                </svg>
                              </div>
                            </div>
                          </div>

                          <!-- ENHANCED: Gaming Border Glow Effect -->
                          <div class="absolute inset-0 rounded-xl border-2 border-transparent group-hover:border-blue-400/60 transition-all duration-300 pointer-events-none"></div>

                          <!-- ENHANCED: Gaming Shadow Glow -->
                          <div class="absolute inset-0 rounded-xl shadow-lg shadow-transparent group-hover:shadow-blue-500/25 transition-all duration-300 pointer-events-none"></div>
                        </div>
                      </a>
                    </div>
                  <?php
                    $slideIndex++; // FIXED: Increment slide index counter
                  }

                  // If no valid images found, show placeholder
                  if (!$hasValidImages) {
                    echo "<!-- DEBUG: No valid images found, showing placeholder -->";
                    ?>
                    <div class="swiper-slide">
                      <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-700/80 to-gray-800/90 rounded-xl border border-gray-600/30">
                        <div class="text-center text-gray-300">
                          <div class="w-24 h-24 bg-gray-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-12 h-12 opacity-60" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                            </svg>
                          </div>
                          <p class="text-base font-medium mb-2">Hình ảnh không khả dụng</p>
                          <p class="text-sm opacity-75">Dữ liệu hình ảnh không hợp lệ</p>
                        </div>
                      </div>
                    </div>
                    <?php
                  }
                } else {
                  echo "<!-- DEBUG: No images found in database or invalid data -->";
                  ?>
                  <!-- ENHANCED: No images fallback with improved styling -->
                  <div class="swiper-slide">
                    <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-700/80 to-gray-800/90 rounded-xl border border-gray-600/30">
                      <div class="text-center text-gray-300">
                        <div class="w-24 h-24 bg-gray-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                          <svg class="w-12 h-12 opacity-60" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                          </svg>
                        </div>
                        <p class="text-base font-medium mb-2">Chưa có hình ảnh</p>
                        <p class="text-sm opacity-75">Tài khoản này chưa có hình ảnh minh họa</p>
                      </div>
                    </div>
                  </div>
                <?php } ?>
              </div>

                <!-- ENHANCED: Gaming Navigation Controls -->
                <button class="swiper-button-prev-ex1 absolute left-4 top-1/2 -translate-y-1/2 z-20 w-12 h-12 bg-black bg-opacity-60 hover:bg-opacity-80 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110 border-2 border-white border-opacity-20 hover:border-opacity-40">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                  </svg>
                </button>

                <button class="swiper-button-next-ex1 absolute right-4 top-1/2 -translate-y-1/2 z-20 w-12 h-12 bg-black bg-opacity-60 hover:bg-opacity-80 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110 border-2 border-white border-opacity-20 hover:border-opacity-40">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                  </svg>
                </button>

                <!-- ENHANCED: Gaming Pagination -->
                <div class="swiper-pagination absolute bottom-4 left-1/2 -translate-x-1/2 z-20"></div>
              </div>
            </div>

            <!-- ENHANCED: Gaming Grid Display (Alternative View) -->
            <div class="gaming-grid-display hidden" id="grid-display">
              <div class="gaming-grid-items">
                <?php
                // ENHANCED: Generate grid items for all images
                if (!empty($imgs) && is_array($imgs)) {
                  $gridIndex = 0;
                  foreach ($imgs as $img) {
                    // Validate image data and provide fallback
                    $imagePath = '';
                    $isValidImage = false;

                    if (isset($img['images']) && !empty(trim($img['images']))) {
                      $imagePath = trim($img['images']);
                      $isValidImage = true;
                    } else {
                      $imagePath = '/assets/image/error_img.png';
                    }

                    $activeClass = $gridIndex === 0 ? 'active' : '';
                    ?>
                    <div class="gaming-grid-item <?php echo $activeClass ?>"
                         data-grid-index="<?php echo $gridIndex ?>"
                         data-image-id="<?php echo htmlspecialchars($img['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                         data-image-src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         data-grid-id="grid-item-<?php echo $gridIndex ?>"
                         title="Click to view image <?php echo $gridIndex + 1 ?> (ID: <?php echo $img['id'] ?? 'N/A' ?>): <?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         style="cursor: pointer;">

                      <a data-fancybox="gallery" href="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         class="block w-full h-full group cursor-pointer">
                        <!-- ENHANCED: Gaming Grid Item Container -->
                        <div class="gaming-grid-item-container">
                          <?php if ($isValidImage): ?>
                            <img src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                                 alt="Grid Image <?php echo $gridIndex + 1 ?>"
                                 class="gaming-grid-item-image"
                                 loading="lazy"
                                 decoding="async"
                                 onload="this.style.opacity='1';"
                                 onerror="this.src='/assets/image/error_img.png';"
                                 style="opacity: 0; transition: opacity 0.3s ease;"
                                 data-grid-index="<?php echo $gridIndex ?>">
                          <?php else: ?>
                            <div class="gaming-grid-item-placeholder">
                              <svg fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                              </svg>
                            </div>
                          <?php endif; ?>

                          <!-- ENHANCED: Gaming Hover Effects -->
                          <div class="gaming-grid-item-overlay">
                            <div class="gaming-grid-item-hover-icon">
                              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                    <?php
                    $gridIndex++;
                  }
                } else {
                  // Show placeholder grid item when no images
                  ?>
                  <div class="gaming-grid-item active" data-grid-index="0">
                    <div class="gaming-grid-item-container">
                      <div class="gaming-grid-item-placeholder">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                  <?php
                }
                ?>
              </div>
            </div>

            <!-- ENHANCED: Gaming Thumbnail Display (Third View) -->
            <div class="gaming-thumbnail-display hidden" id="thumbnail-display">
              <div class="gaming-thumbnail-header">
                <div class="gaming-thumbnail-title">
                  <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                  </svg>
                  Xem Nhanh
                </div>
                <div class="gaming-thumbnail-count">
                  <?php
                  $imageCount = 0;
                  if (!empty($imgs) && is_array($imgs)) {
                    foreach ($imgs as $img) {
                      if (isset($img['images']) && !empty(trim($img['images']))) {
                        $imageCount++;
                      }
                    }
                  }
                  echo $imageCount . ' ảnh';
                  ?>
                </div>
              </div>

              <div class="gaming-main-thumbnail-grid" id="main-thumbnail-container">
                <?php
                // ENHANCED: Generate main thumbnails for all images
                if (!empty($imgs) && is_array($imgs)) {
                  $thumbnailIndex = 0;
                  foreach ($imgs as $img) {
                    // Validate image data and provide fallback
                    $imagePath = '';
                    $isValidImage = false;

                    if (isset($img['images']) && !empty(trim($img['images']))) {
                      $imagePath = trim($img['images']);
                      $isValidImage = true;
                    } else {
                      $imagePath = '/assets/image/error_img.png';
                    }

                    $activeClass = $thumbnailIndex === 0 ? 'active' : '';
                    ?>
                    <div class="gaming-main-thumbnail <?php echo $activeClass ?>"
                         data-slide-index="<?php echo $thumbnailIndex ?>"
                         data-image-id="<?php echo htmlspecialchars($img['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                         data-image-src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         data-thumbnail-id="main-thumbnail-<?php echo $thumbnailIndex ?>"
                         title="Click to view image <?php echo $thumbnailIndex + 1 ?> (ID: <?php echo $img['id'] ?? 'N/A' ?>): <?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         style="cursor: pointer;">

                      <a data-fancybox="gallery" href="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                         class="block w-full h-full group cursor-pointer">
                        <!-- ENHANCED: Main Thumbnail Container -->
                        <div class="gaming-main-thumbnail-container">
                          <!-- ENHANCED: Always show placeholder first, then overlay image -->
                          <div class="gaming-thumbnail-placeholder">
                            <svg fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                            </svg>
                          </div>

                          <?php if ($isValidImage): ?>
                            <!-- ENHANCED: Improved image loading with better error handling -->
                            <img src="<?php echo htmlspecialchars($imagePath, ENT_QUOTES, 'UTF-8') ?>"
                                 alt="Main Thumbnail <?php echo $thumbnailIndex + 1 ?>"
                                 class="gaming-main-thumbnail-image"
                                 loading="lazy"
                                 decoding="async"
                                 style="opacity: 0; transition: opacity 0.3s ease;"
                                 onload="this.style.opacity='1'; this.style.zIndex='2';"
                                 onerror="this.style.display='none';"
                                 data-thumbnail-index="<?php echo $thumbnailIndex ?>">
                          <?php endif; ?>

                          <!-- ENHANCED: Gaming Hover Effects -->
                          <div class="gaming-main-thumbnail-overlay">
                            <div class="gaming-main-thumbnail-hover-icon">
                              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </a>
                    </div>
                    <?php
                    $thumbnailIndex++;
                  }
                } else {
                  // Show placeholder thumbnail when no images
                  ?>
                  <div class="gaming-main-thumbnail active" data-slide-index="0">
                    <div class="gaming-main-thumbnail-container">
                      <div class="gaming-thumbnail-placeholder">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                  <?php
                }
                ?>
              </div>
            </div>

            <!-- ENHANCED: View Toggle Controls with Thumbnail View -->
            <div class="gaming-view-controls">
              <button class="gaming-view-btn active" data-view="featured" title="Featured View">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
              </button>
              <button class="gaming-view-btn" data-view="grid" title="Grid View">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                </svg>
              </button>
              <button class="gaming-view-btn" data-view="thumbnails" title="Thumbnail View">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </button>
            </div>
          </div>


        </div>
      </div>
    </div>
  </div>
    <!-- ENHANCED: Character and Equipment Attributes with Improved Layout -->
    <div class="space-y-8">

      <!-- ENHANCED: Unified Attributes Container with Gaming Aesthetics -->
      <div class="container gaming-attributes-container">
        <div class="gaming-attributes-header">
          <div class="flex items-center">
            <div class="gaming-attributes-icon mr-4">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div>
              <h2 class="gaming-attributes-title">Thuộc Tính Tài Khoản</h2>
              <p class="gaming-attributes-subtitle">Thông tin chi tiết về nhân vật, vũ khí và trang bị</p>
            </div>
          </div>
        </div>

        <!-- ENHANCED: Characters Section with Premium Gaming Design -->
        <?php if ($game['char'] != '') {?>
          <div class="gaming-attribute-section">
            <div class="gaming-attribute-header">
              <div class="gaming-attribute-title-group">
                <div class="gaming-attribute-icon character">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <h3 class="gaming-attribute-title"><?php echo $game['char'] ?></h3>
              </div>
              <span class="gaming-attribute-count character">
                <?php echo format_cash($duogxaolin->count("SELECT `id` FROM `account_attribute` WHERE `account_id` = ? AND `type`= ?", [$acc['id'], 'char'])) ?> <?php echo $game['char'] ?>
              </span>
            </div>

            <div class="gaming-attribute-grid">
              <?php foreach ($duogxaolin->getList("SELECT * FROM `account_attribute` WHERE `account_id` = ? AND `type`= ?", [$acc['id'], 'char']) as $crt) {
                $info_char = $duogxaolin->getRow("SELECT * FROM `game_attribute` WHERE `id` = ?", [$crt['attribute_id']]);
              ?>
                <div class="gaming-attribute-card character gaming-tooltip"
                     data-tooltip="<?php echo htmlspecialchars($info_char['value'], ENT_QUOTES, 'UTF-8') ?>"
                     data-tooltip-theme="character">
                  <!-- Enhanced Background Pattern -->
                  <div class="gaming-attribute-background"
                       style="background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png');"></div>

                  <!-- Character Image -->
                  <img src="<?php echo $info_char['images'] ?>"
                       alt="<?php echo $info_char['value'] ?>"
                       class="gaming-attribute-image">

                  <!-- Enhanced Hover Overlay -->
                  <div class="gaming-attribute-overlay character"></div>

                  <!-- Enhanced Glow Effect -->
                  <div class="gaming-attribute-glow"></div>
                </div>
              <?php } ?>
            </div>
          </div>
        <?php } ?>
        <!-- ENHANCED: Weapons Section with Premium Gaming Design -->
        <?php if ($game['weapon'] != '') {?>
          <div class="gaming-attribute-section">
            <div class="gaming-attribute-header">
              <div class="gaming-attribute-title-group">
                <div class="gaming-attribute-icon weapon">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                </div>
                <h3 class="gaming-attribute-title"><?php echo $game['weapon'] ?></h3>
              </div>
              <span class="gaming-attribute-count weapon">
                <?php echo format_cash($duogxaolin->count("SELECT `id` FROM `account_attribute` WHERE `account_id` = ? AND `type`= ?", [$acc['id'], 'weapon'])) ?> <?php echo $game['weapon'] ?>
              </span>
            </div>

            <div class="gaming-attribute-grid">
              <?php foreach ($duogxaolin->getList("SELECT * FROM `account_attribute` WHERE `account_id` = ? AND `type`= ?", [$acc['id'], 'weapon']) as $crt) {
                $info_char = $duogxaolin->getRow("SELECT * FROM `game_attribute` WHERE `id` = ?", [$crt['attribute_id']]);
              ?>
                <div class="gaming-attribute-card weapon gaming-tooltip"
                     data-tooltip="<?php echo htmlspecialchars($info_char['value'], ENT_QUOTES, 'UTF-8') ?>"
                     data-tooltip-theme="weapon">
                  <!-- Enhanced Background Pattern -->
                  <div class="gaming-attribute-background"
                       style="background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png');"></div>

                  <!-- Weapon Image -->
                  <img src="<?php echo $info_char['images'] ?>"
                       alt="<?php echo $info_char['value'] ?>"
                       class="gaming-attribute-image">

                  <!-- Enhanced Hover Overlay -->
                  <div class="gaming-attribute-overlay weapon"></div>

                  <!-- Enhanced Glow Effect -->
                  <div class="gaming-attribute-glow"></div>
                </div>
              <?php } ?>
            </div>
          </div>
        <?php } ?>
        <!-- ENHANCED: Skins Section with Premium Gaming Design -->
        <?php if ($game['skin'] != '') {?>
          <div class="gaming-attribute-section">
            <div class="gaming-attribute-header">
              <div class="gaming-attribute-title-group">
                <div class="gaming-attribute-icon skin">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <h3 class="gaming-attribute-title"><?php echo $game['skin'] ?></h3>
              </div>
              <span class="gaming-attribute-count skin">
                <?php echo format_cash($duogxaolin->count("SELECT `id` FROM `account_attribute` WHERE `account_id` = ? AND `type`= ?", [$acc['id'], 'skin'])) ?> <?php echo $game['skin'] ?>
              </span>
            </div>

            <div class="gaming-attribute-grid" style="grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));">
              <?php foreach ($duogxaolin->getList("SELECT * FROM `account_attribute` WHERE `account_id` = ? AND `type`= ?", [$acc['id'], 'skin']) as $crt) {
                $info_char = $duogxaolin->getRow("SELECT * FROM `game_attribute` WHERE `id` = ?", [$crt['attribute_id']]);
              ?>
                <div class="gaming-attribute-card skin gaming-tooltip"
                     data-tooltip="<?php echo htmlspecialchars($info_char['value'], ENT_QUOTES, 'UTF-8') ?>"
                     data-tooltip-theme="skin">
                  <!-- Enhanced Background Pattern -->
                  <div class="gaming-attribute-background"
                       style="background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png');"></div>

                  <!-- Skin Image -->
                  <img src="<?php echo $info_char['images'] ?>"
                       alt="<?php echo $info_char['value'] ?>"
                       class="gaming-attribute-image">

                  <!-- Enhanced Hover Overlay -->
                  <div class="gaming-attribute-overlay skin"></div>

                  <!-- Enhanced Glow Effect -->
                  <div class="gaming-attribute-glow"></div>
                </div>
              <?php } ?>
            </div>
          </div>
        <?php } ?>

        <!-- ENHANCED: Empty State Messages with Premium Gaming Design -->
        <div class="space-y-4">
          <?php if ($game['char'] == '') {?>
            <div class="gaming-empty-state">
              <div class="gaming-empty-state-icon">
                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                </svg>
              </div>
              <p class="gaming-empty-state-text">Không có nhân vật</p>
            </div>
          <?php } ?>

          <?php if ($game['weapon'] == '') {?>
            <div class="gaming-empty-state">
              <div class="gaming-empty-state-icon">
                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </div>
              <p class="gaming-empty-state-text">Không có vũ khí</p>
            </div>
          <?php } ?>

          <?php if ($game['skin'] == '') {?>
            <div class="gaming-empty-state">
              <div class="gaming-empty-state-icon">
                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
              </div>
              <p class="gaming-empty-state-text">Không có trang bị</p>
            </div>
          <?php } ?>
        </div>
      </div>
    </div>
  </div>
</div>

</div>

<script src="/assets/js/app/account/optimization.js"></script>




<script>
  // OPTIMIZED: Gaming Features Initialization
  document.addEventListener('DOMContentLoaded', function() {
    // Performance-optimized initialization
    const initializeGamingFeatures = () => {
      const features = [
        { name: 'tooltips', fn: initializeTooltips },
        { name: 'swiper', fn: initializeSwiper, dependency: 'Swiper' },
        { name: 'viewControls', fn: initializeViewControls }
      ];

      features.forEach(feature => {
        try {
          if (feature.dependency && typeof window[feature.dependency] === 'undefined') {
            // Retry with exponential backoff
            let retryCount = 0;
            const maxRetries = 3;
            const retry = () => {
              if (retryCount < maxRetries && typeof window[feature.dependency] !== 'undefined') {
                feature.fn();
              } else if (retryCount < maxRetries) {
                retryCount++;
                setTimeout(retry, Math.pow(2, retryCount) * 500);
              }
            };
            retry();
          } else {
            feature.fn();
          }
        } catch (error) {
          // Silent error handling for production
        }
      });
    };

    // Optimized initialization with performance monitoring
    const initWithFallback = () => {
      if (typeof Swiper !== 'undefined') {
        initializeGamingFeatures();
      } else {
        // Use requestIdleCallback for non-critical retries
        if ('requestIdleCallback' in window) {
          requestIdleCallback(() => initializeGamingFeatures());
        } else {
          setTimeout(initializeGamingFeatures, 100);
        }
      }
    };

    initWithFallback();

    // Single retry with performance check
    setTimeout(() => {
      if (!window.accountSwiper || (window.accountSwiper && window.accountSwiper.destroyed)) {
        initWithFallback();
      }
    }, 1000);

    // ENHANCED: Tooltip initialization function
    function initializeTooltips() {
      if (typeof tippy === 'undefined') {
        return;
      }

      // Performance-optimized tooltip initialization
      const initTooltips = () => {
        const tooltipElements = document.querySelectorAll('[data-tippy-content], [title]');

        tooltipElements.forEach((element) => {
          try {
            const content = element.getAttribute('data-tippy-content') || element.getAttribute('title');
            if (content) {
              tippy(element, {
                content: content,
                theme: element.getAttribute('data-theme') || 'gaming',
                animation: 'scale-subtle',
                placement: element.getAttribute('data-placement') || 'top',
                delay: [200, 100],
                duration: [200, 150],
                // Performance optimizations
                appendTo: () => document.body,
                lazy: true
              });
            }
          } catch (error) {
            // Silent error handling for production
          }
        });
      };

      // Use requestIdleCallback for non-critical initialization
      if ('requestIdleCallback' in window) {
        requestIdleCallback(initTooltips);
      } else {
        setTimeout(initTooltips, 100);
      }

      // Initialize gaming-specific tooltips with performance optimization
      const gamingTooltips = [
        { selector: '.gaming-view-btn[data-view="featured"]', content: 'Chế độ xem nổi bật' },
        { selector: '.gaming-view-btn[data-view="grid"]', content: 'Chế độ lưới' },
        { selector: '.gaming-view-btn[data-view="thumbnails"]', content: 'Chế độ thu nhỏ' },
        { selector: '.swiper-button-prev-ex1', content: 'Ảnh trước' },
        { selector: '.swiper-button-next-ex1', content: 'Ảnh tiếp theo' }
      ];

      const initGamingTooltips = () => {
        gamingTooltips.forEach(({ selector, content }) => {
          const elements = document.querySelectorAll(selector);
          elements.forEach(element => {
            try {
              tippy(element, {
                content: content,
                theme: 'gaming',
                animation: 'scale-subtle',
                placement: 'top',
                duration: [200, 150],
                appendTo: () => document.body
              });
            } catch (error) {
              // Silent error handling for production
            }
          });
        });
      };

      // Defer gaming tooltips initialization
      if ('requestIdleCallback' in window) {
        requestIdleCallback(initGamingTooltips);
      } else {
        setTimeout(initGamingTooltips, 200);
      }
    }

    // Swiper initialization function
    function initializeSwiper() {
      if (typeof Swiper === 'undefined') {
        return false;
      }

      // Find Swiper container
      let swiperContainer = null;
      const containerSelectors = [
        '.mySwiper',
        '#featured-display .swiper',
        '.swiper',
        '.gaming-featured-display .swiper'
      ];

      for (const selector of containerSelectors) {
        try {
          const container = document.querySelector(selector);
          if (container) {
            swiperContainer = container;
            break;
          }
        } catch (error) {
          // Silent error handling for production
        }
      }

      if (!swiperContainer) {
        // Create fallback message
        const featuredDisplay = document.getElementById('featured-display');
        if (featuredDisplay) {
          featuredDisplay.innerHTML = `
            <div class="w-full h-full flex items-center justify-center bg-gray-800 rounded-lg min-h-96">
              <div class="text-center text-gray-400">
                <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                </svg>
                <p class="text-lg">Gallery container not found</p>
                <p class="text-sm mt-2">Please refresh the page</p>
              </div>
            </div>
          `;
        }
        return;
      }



      // Check for slides
      const slides = swiperContainer.querySelectorAll('.swiper-slide');

      if (slides.length === 0) {
        // Try to create basic slides if wrapper exists but no slides
        const wrapper = swiperContainer.querySelector('.swiper-wrapper');
        if (wrapper && wrapper.childElementCount === 0) {
          wrapper.innerHTML = `
            <div class="swiper-slide">
              <div class="w-full h-96 flex items-center justify-center bg-gray-800 rounded-lg">
                <div class="text-center text-gray-400">
                  <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                  </svg>
                  <p>No images available</p>
                </div>
              </div>
            </div>
          `;
        } else {
          return;
        }
      }

      // ENHANCED: Force gallery container visibility with improved dimensions and layout
      const galleryContainer = document.querySelector('.gaming-image-gallery');
      if (galleryContainer) {
        // ENHANCED: Improved height constraints for better visibility
        galleryContainer.style.minHeight = '400px';
        galleryContainer.style.maxHeight = '85vh';
        galleryContainer.style.height = 'auto';
        galleryContainer.style.display = 'block';
        galleryContainer.style.visibility = 'visible';
        galleryContainer.style.position = 'relative';
        galleryContainer.style.zIndex = '1';
        // FIXED: Proper container layout
        galleryContainer.style.padding = '0';
        galleryContainer.style.margin = '0 auto';
        galleryContainer.style.width = '100%';
        galleryContainer.style.maxWidth = '100%';
        galleryContainer.style.boxSizing = 'border-box';
        // ENHANCED: Enhanced border for better visibility
        galleryContainer.style.border = '3px solid rgba(80, 129, 255, 0.6)';

      }

      // ENHANCED: Initialize Gaming Layout Components after Swiper is ready
      // This will be called after Swiper initialization completes

      // ENHANCED: Setup responsive thumbnail gallery layout
      const thumbnailGallery = document.querySelector('.gaming-thumbnail-gallery');
      if (thumbnailGallery) {
        const isTablet = window.innerWidth >= 769 && window.innerWidth <= 1023;
        const isDesktop = window.innerWidth >= 1024;

        if (isTablet) {
          thumbnailGallery.style.width = '240px';
          thumbnailGallery.style.maxWidth = '240px';
          thumbnailGallery.style.maxHeight = '180px';
        } else if (isDesktop) {
          thumbnailGallery.style.width = '280px';
          thumbnailGallery.style.maxWidth = '280px';
          thumbnailGallery.style.maxHeight = '200px';
        }

      }

      // FIXED: Force thumbnail container proper layout
      const thumbnailContainer = document.querySelector('.gaming-thumbnail-container');
      if (thumbnailContainer) {
        thumbnailContainer.style.width = '100%';
        thumbnailContainer.style.maxWidth = '100%';
        thumbnailContainer.style.overflowX = 'auto';
        thumbnailContainer.style.overflowY = 'hidden';
        thumbnailContainer.style.boxSizing = 'border-box';

      }

      // FIXED: Force swiper container visibility with improved dimensions and overflow prevention
      swiperContainer.style.minHeight = '400px';
      swiperContainer.style.maxHeight = '85vh';
      swiperContainer.style.height = 'auto';
      swiperContainer.style.display = 'block';
      swiperContainer.style.visibility = 'visible';
      swiperContainer.style.position = 'relative';
      swiperContainer.style.zIndex = '2';
      // CRITICAL: Prevent infinite stretching from the start
      swiperContainer.style.width = '100%';
      swiperContainer.style.maxWidth = '100%';
      swiperContainer.style.minWidth = '0';
      swiperContainer.style.overflow = 'hidden';
      swiperContainer.style.boxSizing = 'border-box';

      // Optimize images in slides
      const images = swiperContainer.querySelectorAll('img');

      // Force image visibility and optimize loading
      images.forEach((img) => {
        img.style.display = 'block';
        img.style.visibility = 'visible';
        if (img.complete) {
          img.style.opacity = '1';
        }
      });

      try {
        // ENHANCED: Get updated slides count after potential fallback creation
        const currentSlides = swiperContainer.querySelectorAll('.swiper-slide');


        // FIXED: Robust Swiper configuration with enhanced touch/swipe functionality
        const swiperConfig = {
          slidesPerView: 1,
          spaceBetween: 0, // FIXED: Reduced spacing to prevent overflow
          loop: currentSlides.length > 3, // FIXED: Only enable loop with sufficient slides
          centeredSlides: true,
          effect: 'slide',
          speed: 600,
          grabCursor: true,
          watchSlidesProgress: true,
          watchSlidesVisibility: true,
          // FIXED: Add container size constraints to prevent infinite stretching
          width: null, // Let Swiper calculate width automatically
          height: null, // Let Swiper calculate height automatically
          autoHeight: false, // FIXED: Disable auto height to prevent layout issues

          // OPTIMIZED: Touch/swipe functionality
          allowTouchMove: true,
          touchRatio: window.innerWidth <= 768 ? 1.2 : 1,
          simulateTouch: true,
          touchEventsTarget: 'container',
          resistance: true,
          resistanceRatio: 0.85,
          threshold: window.innerWidth <= 768 ? 5 : 10,
          longSwipes: true,
          longSwipesRatio: 0.5,
          longSwipesMs: window.innerWidth <= 768 ? 200 : 300,
          followFinger: true,
          shortSwipes: true,

          // ENHANCED: Pagination with better error handling
          pagination: {
            el: ".swiper-pagination",
            clickable: true,
            dynamicBullets: true,
            renderBullet: function (index, className) {
              return `<span class="${className} gaming-pagination-bullet" data-index="${index}"></span>`;
            },
          },

          // ENHANCED: Navigation with existence check
          navigation: {
            nextEl: ".swiper-button-next-ex1",
            prevEl: ".swiper-button-prev-ex1",
            disabledClass: 'swiper-button-disabled'
          },

          // ENHANCED: Conditional autoplay
          autoplay: currentSlides.length > 1 ? {
            delay: 5000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
            waitForTransition: true
          } : false,

          keyboard: {
            enabled: true,
            onlyInViewport: true,
          },

          mousewheel: {
            invert: false,
          },

          // FIXED: Responsive breakpoints with enhanced touch/swipe functionality
          breakpoints: {
            // Mobile (320px and up) - Enhanced touch responsiveness
            320: {
              slidesPerView: 1,
              spaceBetween: 0,
              centeredSlides: true,
              loop: currentSlides.length > 3,
              autoplay: currentSlides.length > 1 ? {
                delay: 6000,
                disableOnInteraction: false,
              } : false,
              speed: 400,
              // FIXED: Enhanced mobile touch settings
              allowTouchMove: true,
              touchRatio: 1.2, // More sensitive on mobile
              touchAngle: 45,
              simulateTouch: true,
              touchEventsTarget: 'container',
              touchStartPreventDefault: false,
              threshold: 5, // Lower threshold for mobile
              longSwipes: true,
              longSwipesRatio: 0.3,
              longSwipesMs: 200,
              followFinger: true,
              shortSwipes: true,
              pagination: {
                el: ".swiper-pagination",
                clickable: true,
                dynamicBullets: true,
                dynamicMainBullets: 2,
              },
            },
            // Tablet (768px and up) - Enhanced touch and mouse support
            768: {
              slidesPerView: 1,
              spaceBetween: 0,
              centeredSlides: true,
              loop: currentSlides.length > 3,
              autoplay: currentSlides.length > 1 ? {
                delay: 5000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              } : false,
              speed: 500,
              // FIXED: Enhanced tablet touch/mouse settings
              allowTouchMove: true,
              touchRatio: 1.1,
              touchAngle: 45,
              simulateTouch: true,
              touchEventsTarget: 'container',
              touchStartPreventDefault: false,
              threshold: 8,
              longSwipes: true,
              longSwipesRatio: 0.4,
              longSwipesMs: 250,
              followFinger: true,
              shortSwipes: true,
              pagination: {
                el: ".swiper-pagination",
                clickable: true,
                dynamicBullets: true,
                dynamicMainBullets: 3,
              },
            },
            // Desktop (1024px and up) - Enhanced mouse drag and touch support
            1024: {
              slidesPerView: 1,
              spaceBetween: 0,
              centeredSlides: true,
              loop: currentSlides.length > 3, // FIXED: Only enable loop with sufficient slides
              autoplay: currentSlides.length > 1 ? {
                delay: 4000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              } : false,
              speed: 600,
              // FIXED: Enhanced desktop mouse drag and touch settings
              width: null, // Let container determine width
              height: null, // Let container determine height
              allowTouchMove: true,
              touchRatio: 1,
              touchAngle: 45,
              simulateTouch: true, // Enable mouse drag simulation
              touchEventsTarget: 'container',
              touchStartPreventDefault: false,
              threshold: 10,
              longSwipes: true,
              longSwipesRatio: 0.5,
              longSwipesMs: 300,
              followFinger: true,
              shortSwipes: true,
              resistance: true,
              resistanceRatio: 0.85,
            }
          },

          // Event handlers with touch/swipe functionality
          on: {
            init: function () {
              // Store reference
              window.swiperInstance = this;

              // Enable touch/swipe functionality after initialization
              try {
                // Ensure touch events are properly enabled
                this.allowTouchMove = true;
                this.touchEventsData.isTouchEvent = true;

                // Force enable touch events on container
                if (this.el) {
                  this.el.style.touchAction = 'pan-y pinch-zoom';
                  this.el.style.cursor = 'grab';
                }

                // Force enable touch events on wrapper
                if (this.wrapperEl) {
                  this.wrapperEl.style.touchAction = 'pan-y pinch-zoom';
                  this.wrapperEl.style.willChange = 'transform';
                }
              } catch (touchError) {
                // Silent error handling for production
              }

              // Image loading with error handling
              const images = this.el.querySelectorAll('img');
              let loadedImages = 0;
              const totalImages = images.length;

              images.forEach((img, index) => {
                if (img.complete && img.naturalHeight !== 0) {
                  loadedImages++;
                } else {
                  img.addEventListener('load', () => {
                    loadedImages++;
                    this.update();
                  });

                  img.addEventListener('error', () => {
                    loadedImages++; // Count as processed
                  });

                  // Force image visibility and prevent touch interference
                  img.style.display = 'block';
                  img.style.visibility = 'visible';
                  img.style.opacity = '1';
                  img.style.pointerEvents = 'none';
                  img.style.webkitUserDrag = 'none';
                  img.style.userSelect = 'none';
                }
              });

              // Initialize view controls after Swiper is ready
              setTimeout(() => {
                initializeGamingLayoutComponents();
              }, 100);
            },

            // Touch event handlers
            touchStart: function(swiper, event) {
              try {
                // Change cursor to grabbing
                if (swiper.el) {
                  swiper.el.style.cursor = 'grabbing';
                }
              } catch (error) {
                // Silent error handling for production
              }
            },

            touchEnd: function(swiper, event) {
              try {
                // Reset cursor
                if (swiper.el) {
                  swiper.el.style.cursor = 'grab';
                }
              } catch (error) {
                // Silent error handling for production
              }
            },

            touchMove: function(swiper, event) {
              // Touch move handling
            },

            beforeInit: function() {
              // Swiper before init
            },

            afterInit: function() {
              // Swiper after init - ready for interaction
            },

            slideChange: function () {
              // ENHANCED: Safe slide change effects with thumbnail sync using database IDs
              try {
                console.log('🔄 Swiper slide changed to index:', this.activeIndex);

                // Get the current slide's image ID
                const currentSlide = this.slides[this.activeIndex];
                const currentImageId = currentSlide ? currentSlide.getAttribute('data-image-id') : null;
                console.log('📋 Current slide image ID:', currentImageId);

                // Apply visual effects to slides
                this.slides.forEach((slide, index) => {
                  if (slide && slide.style) {
                    if (index === this.activeIndex) {
                      slide.style.transform = 'scale(1)';
                      slide.style.opacity = '1';
                    } else {
                      slide.style.transform = 'scale(0.98)';
                      slide.style.opacity = '0.8';
                    }
                  }
                });

                // ENHANCED: Update thumbnail active state with both index and image ID
                setTimeout(() => {
                  if (typeof window.updateThumbnailActiveState === 'function') {
                    window.updateThumbnailActiveState(this.activeIndex, currentImageId);
                  } else {
                    console.warn('⚠️ updateThumbnailActiveState function not available');
                  }

                  // ENHANCED: Sync with gaming layout components
                  if (typeof window.syncGamingLayoutWithSlide === 'function') {
                    window.syncGamingLayoutWithSlide(this.activeIndex, currentImageId);
                  }
                }, 50);

              } catch (error) {
                console.error('❌ Slide change effect error:', error);
              }
            },

            error: function(error) {
              console.error('❌ Swiper error:', error);
            }
          }
        };

        // FIXED: Create Swiper instance with comprehensive error handling and sizing fixes

        let swiperInstance = null;

        // FIXED: Pre-initialize container sizing and touch functionality
        try {


          // Force container constraints
          swiperContainer.style.width = '100%';
          swiperContainer.style.maxWidth = '100%';
          swiperContainer.style.overflow = 'hidden';
          swiperContainer.style.position = 'relative';

          // FIXED: Enable touch/swipe functionality
          swiperContainer.style.touchAction = 'pan-y pinch-zoom';
          swiperContainer.style.webkitUserSelect = 'none';
          swiperContainer.style.mozUserSelect = 'none';
          swiperContainer.style.msUserSelect = 'none';
          swiperContainer.style.userSelect = 'none';
          swiperContainer.style.cursor = 'grab';

          // Force wrapper constraints
          const swiperWrapper = swiperContainer.querySelector('.swiper-wrapper');
          if (swiperWrapper) {
            swiperWrapper.style.width = '100%';
            swiperWrapper.style.maxWidth = '100%';
            swiperWrapper.style.transform = 'translate3d(0px, 0px, 0px)';
            swiperWrapper.style.display = 'flex';
            swiperWrapper.style.position = 'relative';
            // FIXED: Enable smooth touch interactions
            swiperWrapper.style.touchAction = 'pan-y pinch-zoom';
            swiperWrapper.style.willChange = 'transform';
          }

          // Force slide constraints and touch settings
          const slides = swiperContainer.querySelectorAll('.swiper-slide');
          slides.forEach(slide => {
            slide.style.width = '100%';
            slide.style.maxWidth = '100%';
            slide.style.flexShrink = '0';
            slide.style.position = 'relative';
            slide.style.boxSizing = 'border-box';
            // FIXED: Enable touch interactions on slides
            slide.style.touchAction = 'pan-y pinch-zoom';
            slide.style.webkitUserSelect = 'none';
            slide.style.mozUserSelect = 'none';
            slide.style.msUserSelect = 'none';
            slide.style.userSelect = 'none';

            // FIXED: Prevent image interference with touch gestures
            const images = slide.querySelectorAll('img');
            images.forEach(img => {
              img.style.pointerEvents = 'none';
              img.style.webkitUserDrag = 'none';
              img.style.khtmlUserDrag = 'none';
              img.style.mozUserDrag = 'none';
              img.style.oUserDrag = 'none';
              img.style.webkitUserSelect = 'none';
              img.style.mozUserSelect = 'none';
              img.style.msUserSelect = 'none';
              img.style.userSelect = 'none';
            });
          });

        } catch (preConfigError) {
          // Silent error handling for production
        }

        try {
          swiperInstance = new Swiper(swiperContainer, swiperConfig);

          // Post-initialization sizing fix
          setTimeout(() => {
            try {
              if (swiperInstance && !swiperInstance.destroyed) {
                // Force update sizing
                swiperInstance.updateSize();
                swiperInstance.updateSlides();
                swiperInstance.updateProgress();
                swiperInstance.updateSlidesClasses();

                // Reset any extreme transform values
                const wrapper = swiperInstance.wrapperEl || swiperContainer.querySelector('.swiper-wrapper');
                if (wrapper) {
                  const currentTransform = wrapper.style.transform;
                  if (currentTransform && (currentTransform.includes('e+') || currentTransform.includes('e-'))) {
                    wrapper.style.transform = 'translate3d(0px, 0px, 0px)';
                    swiperInstance.setTranslate(0);
                    swiperInstance.slideTo(0, 0);
                  }
                }
              }
            } catch (postFixError) {
              // Silent error handling for production
            }
          }, 100);

        } catch (error) {
          return false;
        }

        // FIXED: Store swiper instance globally with proper error handling
        try {
          window.accountSwiper = swiperInstance;
          window.mySwiper = swiperInstance;

          console.log('✅ Swiper instance stored globally:', {
            accountSwiper: !!window.accountSwiper,
            mySwiper: !!window.mySwiper,
            slides: swiperInstance ? swiperInstance.slides.length : 0,
            activeIndex: swiperInstance ? swiperInstance.activeIndex : 'N/A',
            destroyed: swiperInstance ? swiperInstance.destroyed : 'N/A'
          });
        } catch (error) {
          console.error('❌ Error storing Swiper instance globally:', error);
        }

        // FIXED: Verify Swiper is working with proper error handling
        setTimeout(() => {
          try {
            if (swiperInstance && !swiperInstance.destroyed) {
              console.log('🔄 Swiper health check passed');
              swiperInstance.update();
              swiperInstance.updateSize();
              swiperInstance.updateSlides();
            } else {
              console.warn('⚠️ Swiper health check failed - instance not available or destroyed');
            }
          } catch (error) {
            console.error('❌ Swiper health check error:', error);
          }
        }, 100);

        console.log('🎉 Swiper initialization completed successfully');

      } catch (error) {
        console.error('❌ Failed to initialize Swiper:', error);
        console.error('Error details:', {
          message: error.message,
          stack: error.stack
        });

        // FIXED: Comprehensive fallback for when Swiper fails with proper error handling
        console.log('🔄 Setting up fallback Swiper object to prevent further errors...');

        try {
          // Create a safe fallback object that mimics Swiper interface
          window.accountSwiper = {
            destroyed: true,
            slides: [],
            activeIndex: 0,
            el: null,
            update: function() {
              console.warn('⚠️ Swiper fallback: update() called - Swiper failed to initialize');
              return this;
            },
            updateSize: function() {
              console.warn('⚠️ Swiper fallback: updateSize() called - Swiper failed to initialize');
              return this;
            },
            updateSlides: function() {
              console.warn('⚠️ Swiper fallback: updateSlides() called - Swiper failed to initialize');
              return this;
            },
            updateProgress: function() {
              console.warn('⚠️ Swiper fallback: updateProgress() called - Swiper failed to initialize');
              return this;
            },
            updateSlidesClasses: function() {
              console.warn('⚠️ Swiper fallback: updateSlidesClasses() called - Swiper failed to initialize');
              return this;
            },
            slideTo: function(index) {
              console.warn('⚠️ Swiper fallback: slideTo(' + index + ') called - Swiper failed to initialize');
              return this;
            },
            autoplay: {
              running: false,
              stop: function() { console.warn('⚠️ Swiper fallback: autoplay.stop() called'); },
              start: function() { console.warn('⚠️ Swiper fallback: autoplay.start() called'); }
            }
          };
          window.mySwiper = window.accountSwiper;
          console.log('✅ Fallback Swiper object created successfully');
        } catch (fallbackError) {
          console.error('❌ Even fallback creation failed:', fallbackError);
        }

        // Show user-friendly error message
        const fallbackContainer = document.querySelector('.mySwiper') || document.getElementById('featured-display');
        if (fallbackContainer) {
          fallbackContainer.innerHTML = `
            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-700/80 to-gray-800/90 rounded-xl border border-gray-600/30 min-h-96">
              <div class="text-center text-gray-300">
                <div class="w-24 h-24 bg-gray-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-12 h-12 opacity-60" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-lg font-medium mb-2">Đang tải hình ảnh...</p>
                <p class="text-sm opacity-75 mb-4">Vui lòng đợi trong giây lát hoặc tải lại trang</p>
                <button onclick="location.reload()" class="gaming-btn-primary">
                  Tải lại trang
                </button>
              </div>
            </div>
          `;
        }
      }
    }

    // ENHANCED: Gaming Layout Components Initialization
    function initializeGamingLayoutComponents() {
      console.log('🎮 Initializing Gaming Layout Components...');

      try {
        initializeGamingLayout();
        console.log('✅ Gaming layout initialized');
      } catch (error) {
        console.error('❌ Gaming layout initialization failed:', error);
      }

      try {
        initializeViewControls();
        console.log('✅ View controls initialized');
      } catch (error) {
        console.error('❌ View controls initialization failed:', error);
      }

      try {
        initializeSidebarThumbnails();
        console.log('✅ Sidebar thumbnails initialized');
      } catch (error) {
        console.error('❌ Sidebar thumbnails initialization failed:', error);
      }
    }

        // ENHANCED: Initialize thumbnail gallery integration with proper timing
        setTimeout(() => {
          console.log('🔗 Initializing thumbnail gallery integration...');

          // Initialize thumbnail gallery
          if (typeof window.initializeThumbnailGallery === 'function') {
            window.initializeThumbnailGallery();
          } else {
            console.log('ℹ️ initializeThumbnailGallery function not found - using built-in navigation');
          }

          // FIXED: Set initial active thumbnail to match Swiper's active slide using global reference
          try {
            const swiperInstance = window.accountSwiper || swiperInstance;
            if (swiperInstance && !swiperInstance.destroyed) {
              const initialIndex = swiperInstance.activeIndex || 0;
              const initialSlide = swiperInstance.slides[initialIndex];
              const initialImageId = initialSlide ? initialSlide.getAttribute('data-image-id') : null;
              console.log('🎯 Setting initial thumbnail active state to index:', initialIndex, 'Image ID:', initialImageId);

              if (typeof window.updateThumbnailActiveState === 'function') {
                window.updateThumbnailActiveState(initialIndex, initialImageId);
              } else {
                console.log('ℹ️ updateThumbnailActiveState function not found - using built-in sync');
                // Use built-in sync function
                if (typeof syncGamingLayoutWithSlide === 'function') {
                  syncGamingLayoutWithSlide(initialIndex, initialImageId);
                }
              }

              // Verify synchronization
              setTimeout(() => {
                try {
                  console.log('🔍 Verifying thumbnail-swiper synchronization...');
                  console.log('Swiper active index:', swiperInstance.activeIndex);

                  const activeThumbnail = document.querySelector('.gaming-thumbnail.active');
                  if (activeThumbnail) {
                    const thumbIndex = parseInt(activeThumbnail.getAttribute('data-slide-index'));
                    console.log('Active thumbnail index:', thumbIndex);

                    if (thumbIndex === swiperInstance.activeIndex) {
                      console.log('✅ Thumbnail-Swiper synchronization verified');
                    } else {
                      console.warn('⚠️ Thumbnail-Swiper synchronization mismatch');
                    }
                  } else {
                    console.warn('⚠️ No active thumbnail found');
                  }
                } catch (error) {
                  console.error('❌ Error during synchronization verification:', error);
                }
              }, 500);
            } else {
              console.warn('⚠️ Swiper instance not available for initial thumbnail sync');
            }
          } catch (error) {
            console.error('❌ Error setting initial thumbnail state:', error);
          }

        }, 100);

        // FIXED: Force image loading check after Swiper initialization using global reference
        setTimeout(() => {
          try {
            const swiperInstance = window.accountSwiper;
            if (swiperInstance && swiperInstance.el) {
              const allImages = swiperInstance.el.querySelectorAll('img');
              console.log('Post-init: Checking', allImages.length, 'images');

              allImages.forEach((img, index) => {
                if (!img.complete && img.src) {
                  console.log(`Image ${index + 1} still loading:`, img.src);
                  img.onload = () => {
                    console.log(`Image ${index + 1} loaded successfully`);
                    try {
                      if (window.accountSwiper && !window.accountSwiper.destroyed) {
                        window.accountSwiper.update();
                      }
                    } catch (error) {
                      console.error('❌ Error updating Swiper after image load:', error);
                    }
                  };
                } else if (img.complete) {
                  console.log(`Image ${index + 1} already loaded:`, img.src);
                  img.style.opacity = '1';
                }
              });
            }
          } catch (error) {
            console.error('❌ Error during post-init image check:', error);
          }
        }, 1000);

        // FIXED: Handle window resize with proper global reference and infinite stretching prevention
        let resizeTimeout;
        window.addEventListener('resize', () => {
          clearTimeout(resizeTimeout);
          resizeTimeout = setTimeout(() => {
            try {
              if (window.accountSwiper && !window.accountSwiper.destroyed) {
                // FIXED: Check for infinite stretching before updating
                const wrapper = window.accountSwiper.wrapperEl || document.querySelector('.swiper-wrapper');
                if (wrapper) {
                  const currentTransform = wrapper.style.transform;
                  if (currentTransform && (currentTransform.includes('e+') || currentTransform.includes('e-'))) {
                    console.warn('⚠️ Infinite stretching detected during resize, fixing...');
                    wrapper.style.transform = 'translate3d(0px, 0px, 0px)';
                    window.accountSwiper.setTranslate(0);
                    window.accountSwiper.slideTo(0, 0);
                  }
                }

                window.accountSwiper.updateSize();
                window.accountSwiper.updateSlides();
                window.accountSwiper.updateProgress();
                window.accountSwiper.updateSlidesClasses();
                console.log('Swiper updated for new screen size');
              }
            } catch (error) {
              console.error('❌ Error updating Swiper on resize:', error);
            }
          }, 250);
        });

        // FIXED: Add infinite stretching monitoring
        function monitorSwiperStretching() {
          if (window.accountSwiper && !window.accountSwiper.destroyed) {
            try {
              const wrapper = window.accountSwiper.wrapperEl || document.querySelector('.swiper-wrapper');
              const slides = document.querySelectorAll('.swiper-slide');

              if (wrapper) {
                const currentTransform = wrapper.style.transform;
                const wrapperWidth = wrapper.offsetWidth;
                const containerWidth = wrapper.parentElement ? wrapper.parentElement.offsetWidth : 0;

                // Check for extreme transform values
                if (currentTransform && (currentTransform.includes('e+') || currentTransform.includes('e-'))) {
                  console.warn('🚨 INFINITE STRETCHING DETECTED! Fixing immediately...');
                  console.log('Problematic transform:', currentTransform);

                  // Reset transform
                  wrapper.style.transform = 'translate3d(0px, 0px, 0px)';
                  window.accountSwiper.setTranslate(0);
                  window.accountSwiper.slideTo(0, 0);

                  // Force container constraints
                  wrapper.style.width = '100%';
                  wrapper.style.maxWidth = '100%';

                  console.log('✅ Infinite stretching fixed');
                }

                // Check for excessive slide widths
                slides.forEach((slide, index) => {
                  const slideWidth = slide.offsetWidth;
                  if (slideWidth > containerWidth * 2) {
                    console.warn(`🚨 Slide ${index} has excessive width: ${slideWidth}px, fixing...`);
                    slide.style.width = '100%';
                    slide.style.maxWidth = '100%';
                    slide.style.flexShrink = '0';
                  }
                });
              }
            } catch (error) {
              console.error('❌ Error monitoring Swiper stretching:', error);
            }
          }
        }

        // Run monitoring every 2 seconds
        setInterval(monitorSwiperStretching, 2000);

        // Run immediate check
        setTimeout(monitorSwiperStretching, 1000);

        // FIXED: Handle orientation change with proper global reference
        window.addEventListener('orientationchange', () => {
          setTimeout(() => {
            try {
              if (window.accountSwiper && !window.accountSwiper.destroyed) {
                window.accountSwiper.updateSize();
                window.accountSwiper.updateSlides();
                console.log('Swiper updated for orientation change');
              }
            } catch (error) {
              console.error('❌ Error updating Swiper on orientation change:', error);
            }
          }, 500);
        });

     

    // ENHANCED: Final initialization and debugging
    console.log('🎮 Gaming Account Page JavaScript initialization completed');

    // Essential debugging functions for production
    window.debugGamingFeatures = function() {
      return {
        accountSwiper: !!window.accountSwiper,
        mySwiper: !!window.mySwiper,
        destroyed: window.accountSwiper ? window.accountSwiper.destroyed : 'N/A',
        allowTouchMove: window.accountSwiper ? window.accountSwiper.allowTouchMove : 'N/A'
      };
    };

    window.testTouchSwipe = function() {
      if (!window.accountSwiper) {
        return false;
      }

      const swiper = window.accountSwiper;
      return {
        swiperExists: !!swiper,
        allowTouchMove: swiper.allowTouchMove,
        touchEventsEnabled: !!swiper.touchEventsData,
        simulateTouch: swiper.params.simulateTouch,
        slidesCount: swiper.slides ? swiper.slides.length : 0,
        currentSlide: swiper.activeIndex || 0
      };
    };

    // Auto-run debug if issues detected
    setTimeout(() => {
      if (!window.accountSwiper && !window.mySwiper) {
        console.warn('⚠️ No Swiper instance found after initialization');
        window.debugGamingFeatures();
      }
    }, 2000);

    // ENHANCED: Comprehensive functionality test
    window.testGamingFeatures = function() {
      console.log('🧪 Running comprehensive gaming features test...');

      const tests = {
        swiperInstance: !!window.accountSwiper || !!window.mySwiper,
        swiperWorking: false,
        viewControls: document.querySelectorAll('.gaming-view-btn').length > 0,
        tooltips: typeof tippy !== 'undefined',
        thumbnails: document.querySelectorAll('.gaming-thumbnail').length > 0,
        responsiveCSS: true,
        crossBrowser: true
      };

      // Test Swiper functionality
      if (tests.swiperInstance) {
        const swiper = window.accountSwiper || window.mySwiper;
        tests.swiperWorking = !swiper.destroyed && swiper.slides.length > 0;
      }

      // Test view controls
      if (tests.viewControls) {
        const featuredBtn = document.querySelector('.gaming-view-btn[data-view="featured"]');
        if (featuredBtn) {
          featuredBtn.click();
          tests.viewControlsWorking = !document.getElementById('featured-display').classList.contains('hidden');
        }
      }

      // Test tooltips
      if (tests.tooltips) {
        const tooltipElements = document.querySelectorAll('[data-tippy-content]');
        tests.tooltipsWorking = tooltipElements.length > 0;
      }

      console.log('🧪 Test Results:', tests);

      const passedTests = Object.values(tests).filter(Boolean).length;
      const totalTests = Object.keys(tests).length;

      console.log(`🎯 Test Summary: ${passedTests}/${totalTests} tests passed`);

      if (passedTests === totalTests) {
        console.log('🎉 All gaming features are working correctly!');
      } else {
        console.warn('⚠️ Some features may not be working properly');
      }

      return tests;
    };

  

  // ===== ENHANCED GAMING LAYOUT FUNCTIONS =====

  // ENHANCED: Thumbnail Gallery Initialization Function
  window.initializeThumbnailGallery = function() {
    console.log('🎮 Initializing Thumbnail Gallery...');

    const thumbnails = document.querySelectorAll('.gaming-thumbnail');
    console.log(`📋 Found ${thumbnails.length} thumbnails`);

    thumbnails.forEach((thumbnail, index) => {
      thumbnail.addEventListener('click', function() {
        const slideIndex = parseInt(this.getAttribute('data-slide-index'));
        const imageId = this.getAttribute('data-image-id');

        console.log(`🖱️ Thumbnail clicked: Index ${slideIndex}, ID ${imageId}`);

        // Update thumbnail states
        thumbnails.forEach(thumb => thumb.classList.remove('active'));
        this.classList.add('active');

        // Navigate to slide
        if (typeof window.switchToSlide === 'function') {
          window.switchToSlide(slideIndex, imageId);
        }
      });
    });

    console.log('✅ Thumbnail gallery initialized');
  };

  // ENHANCED: Update Thumbnail Active State Function
  window.updateThumbnailActiveState = function(activeIndex, imageId = null) {
    console.log(`🎯 Updating thumbnail active state: Index ${activeIndex}, ID ${imageId}`);

    const thumbnails = document.querySelectorAll('.gaming-thumbnail');

    // Remove active class from all thumbnails
    thumbnails.forEach(thumbnail => {
      thumbnail.classList.remove('active');
    });

    // Try to find thumbnail by image ID first
    if (imageId) {
      const targetThumbnail = document.querySelector(`[data-image-id="${imageId}"]`);
      if (targetThumbnail) {
        targetThumbnail.classList.add('active');
        console.log('✅ Thumbnail activated by image ID');
        return;
      }
    }

    // Fallback to index-based activation
    const indexBasedThumbnail = document.querySelector(`[data-slide-index="${activeIndex}"]`);
    if (indexBasedThumbnail) {
      indexBasedThumbnail.classList.add('active');
      console.log('✅ Thumbnail activated by index');
    } else {
      console.warn('⚠️ No thumbnail found for index:', activeIndex);
    }
  };

  function initializeGamingLayout() {
    console.log('🎮 Initializing Gaming Layout...');

    // Set default view to featured
    const featuredDisplay = document.getElementById('featured-display');
    const gridDisplay = document.getElementById('grid-display');

    if (featuredDisplay && gridDisplay) {
      featuredDisplay.classList.remove('hidden');
      gridDisplay.classList.add('hidden');
      console.log('✅ Featured display set as default');

      // Ensure Swiper container is visible
      const swiperContainer = featuredDisplay.querySelector('.mySwiper');
      if (swiperContainer) {
        swiperContainer.style.display = 'block';
        swiperContainer.style.visibility = 'visible';
        swiperContainer.style.opacity = '1';
        console.log('✅ Swiper container visibility ensured');
      }
    }

    // Force update Swiper if it exists
    if (window.mySwiper && !window.mySwiper.destroyed) {
      setTimeout(() => {
        window.mySwiper.update();
        window.mySwiper.updateSize();
        window.mySwiper.updateSlides();
        console.log('🔄 Swiper updated after gaming layout initialization');
      }, 100);
    }
  }

  function initializeViewControls() {
    console.log('🎮 Initializing View Controls...');

    // ENHANCED: Comprehensive element detection with debugging
    const viewButtons = document.querySelectorAll('.gaming-view-btn');
    const featuredDisplay = document.getElementById('featured-display');
    const gridDisplay = document.getElementById('grid-display');
    const thumbnailDisplay = document.getElementById('thumbnail-display');

    console.log('🔍 View control elements found:', {
      viewButtons: viewButtons.length,
      featuredDisplay: !!featuredDisplay,
      gridDisplay: !!gridDisplay,
      thumbnailDisplay: !!thumbnailDisplay
    });

    if (viewButtons.length === 0) {
      console.warn('⚠️ No view control buttons found');
      return;
    }

    // ENHANCED: Add click handlers with comprehensive error handling
    viewButtons.forEach((button, index) => {
      console.log(`🔘 Setting up button ${index + 1}:`, {
        dataView: button.getAttribute('data-view'),
        className: button.className
      });

      button.addEventListener('click', function(event) {
        event.preventDefault();
        event.stopPropagation();

        const view = this.getAttribute('data-view');
        console.log(`🖱️ View button clicked: ${view}`);

        // ENHANCED: Update button states with visual feedback
        viewButtons.forEach(btn => {
          btn.classList.remove('active');
          btn.style.transform = '';
        });
        this.classList.add('active');
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
          this.style.transform = '';
        }, 150);

        // ENHANCED: Switch views with better error handling
        try {
          if (view === 'featured') {
            featuredDisplay?.classList.remove('hidden');
            gridDisplay?.classList.add('hidden');
            thumbnailDisplay?.classList.add('hidden');
            console.log('🖼️ Switched to Featured View');

            // Update Swiper if it exists
            if (window.mySwiper && !window.mySwiper.destroyed) {
              setTimeout(() => {
                window.mySwiper.update();
                window.mySwiper.updateSize();
              }, 100);
            }

          } else if (view === 'grid') {
            featuredDisplay?.classList.add('hidden');
            gridDisplay?.classList.remove('hidden');
            thumbnailDisplay?.classList.add('hidden');
            console.log('🔲 Switched to Grid View');

          } else if (view === 'thumbnails') {
            featuredDisplay?.classList.add('hidden');
            gridDisplay?.classList.add('hidden');
            thumbnailDisplay?.classList.remove('hidden');
            console.log('🖼️ Switched to Thumbnail View');

            // Initialize main thumbnails after switching to thumbnail view
            setTimeout(() => {
              if (typeof initializeMainThumbnails === 'function') {
                initializeMainThumbnails();
              }
            }, 100);
          }
        } catch (error) {
          console.error('❌ Error switching views:', error);
        }
      });

      // ENHANCED: Add keyboard support
      button.addEventListener('keydown', function(event) {
        if (event.key === 'Enter' || event.key === ' ') {
          event.preventDefault();
          this.click();
        }
      });
    });

    console.log('✅ View controls initialized with', viewButtons.length, 'buttons');
  }

  function initializeSidebarThumbnails() {
    console.log('🎮 Initializing Grid Items Navigation...');

    const gridItems = document.querySelectorAll('.gaming-grid-item');

    // Handle grid item clicks
    gridItems.forEach((item, index) => {
      item.addEventListener('click', function() {
        const gridIndex = this.getAttribute('data-grid-index');
        const imageId = this.getAttribute('data-image-id');

        console.log(`🖱️ Grid item clicked: Index ${gridIndex}, ID ${imageId}`);

        // Update grid item states
        gridItems.forEach(gridItem => gridItem.classList.remove('active'));
        this.classList.add('active');

        // Update main thumbnail states
        const mainThumbnails = document.querySelectorAll('.gaming-main-thumbnail');
        mainThumbnails.forEach(thumb => thumb.classList.remove('active'));
        const targetMainThumbnail = document.querySelector(`[data-slide-index="${gridIndex}"]`);
        if (targetMainThumbnail) {
          targetMainThumbnail.classList.add('active');
        }

        // Navigate Swiper to corresponding slide
        if (window.mySwiper && gridIndex !== null) {
          window.mySwiper.slideTo(parseInt(gridIndex));
          console.log(`🔄 Swiper navigated to slide ${gridIndex}`);
        }
      });
    });

    console.log('✅ Grid items navigation initialized');
  }

  function initializeMainThumbnails() {
    console.log('🎮 Initializing Main Thumbnails...');

    const mainThumbnails = document.querySelectorAll('.gaming-main-thumbnail');
    const gridItems = document.querySelectorAll('.gaming-grid-item');
    const sidebarThumbnails = document.querySelectorAll('.gaming-sidebar-thumbnail');

    // Handle main thumbnail clicks
    mainThumbnails.forEach((thumbnail, index) => {
      thumbnail.addEventListener('click', function(e) {
        // Prevent fancybox from opening immediately
        e.preventDefault();

        const slideIndex = this.getAttribute('data-slide-index');
        const imageId = this.getAttribute('data-image-id');

        console.log(`🖱️ Main thumbnail clicked: Index ${slideIndex}, ID ${imageId}`);

        // Update main thumbnail states
        mainThumbnails.forEach(thumb => thumb.classList.remove('active'));
        this.classList.add('active');

        // Update grid item states
        gridItems.forEach(item => item.classList.remove('active'));
        const targetGridItem = document.querySelector(`[data-grid-index="${slideIndex}"]`);
        if (targetGridItem) {
          targetGridItem.classList.add('active');
        }

        // Update sidebar thumbnail states
        sidebarThumbnails.forEach(thumb => thumb.classList.remove('active'));
        const targetSidebarThumbnail = document.querySelector(`[data-slide-index="${slideIndex}"]`);
        if (targetSidebarThumbnail) {
          targetSidebarThumbnail.classList.add('active');
        }

        // Navigate Swiper to corresponding slide
        if (window.mySwiper && slideIndex !== null) {
          window.mySwiper.slideTo(parseInt(slideIndex));
          console.log(`🔄 Swiper navigated to slide ${slideIndex}`);
        }

        // Open fancybox after a short delay
        setTimeout(() => {
          const fancyboxLink = this.querySelector('a[data-fancybox]');
          if (fancyboxLink) {
            fancyboxLink.click();
          }
        }, 100);
      });
    });

    console.log('✅ Main thumbnails initialized');
  }

  function syncGamingLayoutWithSlide(slideIndex, imageId = null) {
    console.log(`🔄 Syncing gaming layout with slide ${slideIndex}, Image ID: ${imageId}`);

    // Update sidebar thumbnails
    const sidebarThumbnails = document.querySelectorAll('.gaming-sidebar-thumbnail');
    sidebarThumbnails.forEach(thumb => {
      thumb.classList.remove('active');
      const thumbSlideIndex = thumb.getAttribute('data-slide-index');
      if (thumbSlideIndex === slideIndex.toString()) {
        thumb.classList.add('active');
      }
    });

    // Update grid items
    const gridItems = document.querySelectorAll('.gaming-grid-item');
    gridItems.forEach(item => {
      item.classList.remove('active');
      const itemGridIndex = item.getAttribute('data-grid-index');
      if (itemGridIndex === slideIndex.toString()) {
        item.classList.add('active');
      }
    });

    // Update main thumbnails
    const mainThumbnails = document.querySelectorAll('.gaming-main-thumbnail');
    mainThumbnails.forEach(thumb => {
      thumb.classList.remove('active');
      const thumbSlideIndex = thumb.getAttribute('data-slide-index');
      if (thumbSlideIndex === slideIndex.toString()) {
        thumb.classList.add('active');
      }
    });

    console.log('✅ Gaming layout synced with slide');
  }

  // ENHANCED: Database ID-based Thumbnail Gallery Navigation Functions
    window.switchToSlide = function(slideIndex, imageId = null) {
      console.log('🎯 Switching to slide:', slideIndex, 'Image ID:', imageId);

      if (window.accountSwiper && !window.accountSwiper.destroyed) {
        console.log('✅ Swiper instance found, navigating...');

        let targetSlideIndex = slideIndex;

        // ENHANCED: If imageId is provided, find the corresponding slide
        if (imageId !== null && imageId !== '') {
          console.log('🔍 Searching for slide with image ID:', imageId);

          const slides = window.accountSwiper.slides;
          let foundIndex = -1;

          for (let i = 0; i < slides.length; i++) {
            const slide = slides[i];
            const slideImageId = slide.getAttribute('data-image-id');
            console.log(`Slide ${i}: data-image-id = "${slideImageId}"`);

            if (slideImageId === imageId.toString()) {
              foundIndex = i;
              console.log('✅ Found matching slide at index:', foundIndex);
              break;
            }
          }

          if (foundIndex !== -1) {
            targetSlideIndex = foundIndex;
            console.log('🎯 Using ID-based navigation to slide:', targetSlideIndex);
          } else {
            console.warn('⚠️ No slide found with image ID:', imageId, 'falling back to index-based navigation');
          }
        }

        // Validate slide index
        if (typeof targetSlideIndex !== 'number' || targetSlideIndex < 0) {
          console.error('❌ Invalid slide index:', targetSlideIndex);
          return;
        }

        // Get total slides for validation
        const totalSlides = window.accountSwiper.slides.length;
        console.log('📊 Total slides:', totalSlides, 'Target index:', targetSlideIndex);

        if (targetSlideIndex >= totalSlides) {
          console.warn('⚠️ Slide index out of bounds:', targetSlideIndex, 'Max:', totalSlides - 1);
          targetSlideIndex = totalSlides - 1;
        }

        // Disable autoplay temporarily to prevent conflicts
        if (window.accountSwiper.autoplay && window.accountSwiper.autoplay.running) {
          window.accountSwiper.autoplay.stop();
          console.log('⏸️ Autoplay stopped for manual navigation');
        }

        // Switch to the specified slide with animation
        window.accountSwiper.slideTo(targetSlideIndex, 500); // 500ms transition

        // Update thumbnail active state immediately
        updateThumbnailActiveState(targetSlideIndex, imageId);

        // Re-enable autoplay after a delay
        setTimeout(() => {
          if (window.accountSwiper && window.accountSwiper.autoplay) {
            window.accountSwiper.autoplay.start();
            console.log('▶️ Autoplay resumed');
          }
        }, 3000);

        console.log('✅ Navigation completed to slide:', targetSlideIndex);
      } else {
        console.error('❌ Swiper instance not available for slide switching');
        console.log('Swiper state:', {
          exists: !!window.accountSwiper,
          destroyed: window.accountSwiper ? window.accountSwiper.destroyed : 'N/A'
        });
      }
    };

    window.updateThumbnailActiveState = function(activeIndex, imageId = null) {
      console.log('🔄 Updating thumbnail active state to index:', activeIndex, 'Image ID:', imageId);

      // Remove active class from all thumbnails
      const thumbnails = document.querySelectorAll('.gaming-thumbnail');
      console.log('📋 Found', thumbnails.length, 'thumbnails to update');

      let activeThumbnail = null;
      let matchMethod = 'none';

      // ENHANCED: Try to match by image ID first, then fall back to index
      if (imageId !== null && imageId !== '') {
        console.log('🔍 Searching for thumbnail with image ID:', imageId);

        thumbnails.forEach((thumbnail, index) => {
          const thumbImageId = thumbnail.getAttribute('data-image-id');
          const slideIndex = parseInt(thumbnail.getAttribute('data-slide-index'));

          if (thumbImageId === imageId.toString()) {
            thumbnail.classList.add('active');
            activeThumbnail = thumbnail;
            matchMethod = 'id';
            console.log('✅ Activated thumbnail by ID:', imageId, 'at index:', slideIndex);
          } else {
            thumbnail.classList.remove('active');
          }
        });
      }

      // Fallback to index-based matching if ID matching failed
      if (!activeThumbnail) {
        console.log('🔄 Falling back to index-based thumbnail matching');

        thumbnails.forEach((thumbnail, index) => {
          const slideIndex = parseInt(thumbnail.getAttribute('data-slide-index'));

          if (slideIndex === activeIndex) {
            thumbnail.classList.add('active');
            activeThumbnail = thumbnail;
            matchMethod = 'index';
            console.log('✅ Activated thumbnail by index:', slideIndex);
          } else {
            thumbnail.classList.remove('active');
          }
        });
      }

      // Scroll thumbnail into view if needed
      if (activeThumbnail) {
        console.log('📍 Scrolling active thumbnail into view (matched by:', matchMethod + ')');
        activeThumbnail.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center'
        });
      } else {
        console.warn('⚠️ No thumbnail found for active index:', activeIndex, 'or image ID:', imageId);

        // Debug: Log all thumbnail data
        thumbnails.forEach((thumb, idx) => {
          const slideIdx = parseInt(thumb.getAttribute('data-slide-index'));
          const thumbImageId = thumb.getAttribute('data-image-id');
          console.log(`Thumbnail ${idx}: data-slide-index=${slideIdx}, data-image-id=${thumbImageId}`);
        });
      }
    };

    // ENHANCED: Initialize thumbnail gallery interactions with debugging
    window.initializeThumbnailGallery = function() {
      const thumbnails = document.querySelectorAll('.gaming-thumbnail');
      console.log('🔍 Initializing thumbnail gallery...');
      console.log('Found', thumbnails.length, 'thumbnail containers');

      thumbnails.forEach((thumbnail, index) => {
        // Debug thumbnail content
        const img = thumbnail.querySelector('img');
        const placeholder = thumbnail.querySelector('.gaming-thumbnail-placeholder');
        const imageSrc = thumbnail.getAttribute('data-image-src');

        console.log(`Thumbnail ${index + 1}:`, {
          container: thumbnail,
          image: img,
          imageSrc: imageSrc,
          placeholder: placeholder,
          hasImage: !!img,
          imageLoaded: img ? img.complete : false
        });

        // Force thumbnail dimensions
        thumbnail.style.width = '80px';
        thumbnail.style.height = '60px';
        thumbnail.style.minWidth = '80px';
        thumbnail.style.minHeight = '60px';
        thumbnail.style.display = 'flex';
        thumbnail.style.position = 'relative';

        // ENHANCED: Add click event listener with database ID support
        thumbnail.addEventListener('click', function(event) {
          event.preventDefault();
          event.stopPropagation();

          const slideIndex = parseInt(this.getAttribute('data-slide-index'));
          const imageId = this.getAttribute('data-image-id');
          console.log('🖱️ Thumbnail clicked - Index:', slideIndex, 'Image ID:', imageId, 'Element:', this);

          // Validate the slide index
          if (isNaN(slideIndex)) {
            console.error('❌ Invalid slide index from thumbnail:', this.getAttribute('data-slide-index'));
            return;
          }

          // Validate image ID
          if (!imageId || imageId === '') {
            console.warn('⚠️ No image ID found, using index-based navigation only');
          }

          // Add visual feedback
          this.style.transform = 'scale(0.95)';
          setTimeout(() => {
            this.style.transform = '';
          }, 150);

          // Navigate to slide using both index and ID
          switchToSlide(slideIndex, imageId);
        });

        // ENHANCED: Add keyboard navigation with database ID support
        thumbnail.addEventListener('keydown', function(e) {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            e.stopPropagation();

            const slideIndex = parseInt(this.getAttribute('data-slide-index'));
            const imageId = this.getAttribute('data-image-id');
            console.log('⌨️ Thumbnail keyboard activated - Index:', slideIndex, 'Image ID:', imageId);

            if (isNaN(slideIndex)) {
              console.error('❌ Invalid slide index from keyboard:', this.getAttribute('data-slide-index'));
              return;
            }

            // Navigate using both index and ID
            switchToSlide(slideIndex, imageId);
          }
        });

        // Make thumbnails focusable for accessibility
        thumbnail.setAttribute('tabindex', '0');
        thumbnail.setAttribute('role', 'button');
        thumbnail.setAttribute('aria-label', `View image ${index + 1}`);

        // ENHANCED: Force image loading if present
        if (img && imageSrc) {
          console.log(`🖼️ Processing thumbnail image ${index + 1}:`, imageSrc);

          // Force image styles
          img.style.position = 'absolute';
          img.style.top = '0';
          img.style.left = '0';
          img.style.width = '100%';
          img.style.height = '100%';
          img.style.objectFit = 'cover';
          img.style.zIndex = '2';

          // Check if image is already loaded
          if (img.complete && img.naturalHeight !== 0) {
            console.log(`✅ Thumbnail ${index + 1} already loaded`);
            img.style.opacity = '1';
          } else {
            console.log(`⏳ Thumbnail ${index + 1} loading...`);
            // Force reload if needed
            const originalSrc = img.src;
            img.src = '';
            img.src = originalSrc;
          }
        }
      });

      console.log('✅ Thumbnail gallery initialized with', thumbnails.length, 'thumbnails');
    };

    // ENHANCED: Initialize thumbnail gallery with multiple attempts
    setTimeout(() => {
      initializeThumbnailGallery();

      // Add thumbnail debug function
      window.debugThumbnails = function() {
        console.log('=== THUMBNAIL DEBUG INFO ===');
        const thumbnails = document.querySelectorAll('.gaming-thumbnail');
        const thumbnailContainer = document.querySelector('.gaming-thumbnail-container');

        console.log('Thumbnail container:', thumbnailContainer);
        console.log('Thumbnails found:', thumbnails.length);

        thumbnails.forEach((thumb, index) => {
          const img = thumb.querySelector('img');
          const placeholder = thumb.querySelector('.gaming-thumbnail-placeholder');
          const computedStyle = window.getComputedStyle(thumb);

          console.log(`Thumbnail ${index + 1}:`, {
            element: thumb,
            dimensions: {
              width: computedStyle.width,
              height: computedStyle.height,
              display: computedStyle.display,
              position: computedStyle.position
            },
            image: img ? {
              src: img.src,
              complete: img.complete,
              naturalWidth: img.naturalWidth,
              naturalHeight: img.naturalHeight,
              style: img.style.cssText
            } : null,
            placeholder: !!placeholder,
            dataImageSrc: thumb.getAttribute('data-image-src')
          });
        });
      };

      // Auto-debug after 2 seconds
      setTimeout(() => {
        console.log('🔍 Auto-debugging thumbnails...');
        window.debugThumbnails();

        // Test thumbnail navigation
        window.testThumbnailNavigation();
      }, 2000);

      // ENHANCED: Add window resize handler for responsive thumbnail gallery
      window.addEventListener('resize', function() {
        const thumbnailGallery = document.querySelector('.gaming-thumbnail-gallery');
        if (thumbnailGallery) {
          const isMobile = window.innerWidth <= 768;
          const isTablet = window.innerWidth > 768 && window.innerWidth <= 1023;
          const isDesktop = window.innerWidth >= 1024;

          if (isMobile) {
            thumbnailGallery.style.display = 'none';
          } else {
            thumbnailGallery.style.display = 'block';
            thumbnailGallery.style.position = 'absolute';
            thumbnailGallery.style.top = '1rem';
            thumbnailGallery.style.right = '1rem';
            thumbnailGallery.style.zIndex = '30';

            if (isTablet) {
              thumbnailGallery.style.width = '240px';
              thumbnailGallery.style.maxWidth = '240px';
              thumbnailGallery.style.maxHeight = '180px';
            } else if (isDesktop) {
              thumbnailGallery.style.width = '280px';
              thumbnailGallery.style.maxWidth = '280px';
              thumbnailGallery.style.maxHeight = '200px';
            }
          }
          console.log('🔄 Thumbnail gallery layout updated on resize');
        }
      });

      // ENHANCED: Test thumbnail navigation function
      window.testThumbnailNavigation = function() {
        console.log('🧪 Testing thumbnail navigation...');

        const thumbnails = document.querySelectorAll('.gaming-thumbnail');
        const swiper = window.accountSwiper;

        console.log('Test results:', {
          thumbnailCount: thumbnails.length,
          swiperExists: !!swiper,
          swiperSlides: swiper ? swiper.slides.length : 0,
          swiperDestroyed: swiper ? swiper.destroyed : 'N/A'
        });

        // Test first thumbnail click simulation
        if (thumbnails.length > 0) {
          const firstThumbnail = thumbnails[0];
          const slideIndex = parseInt(firstThumbnail.getAttribute('data-slide-index'));
          console.log('🎯 Testing navigation to slide:', slideIndex);

          // Simulate click without actually triggering it
          if (typeof switchToSlide === 'function') {
            console.log('✅ switchToSlide function is available');
          } else {
            console.error('❌ switchToSlide function is NOT available');
          }
        }
      };

    }, 600);

    // ENHANCED: Debug function for troubleshooting gallery issues
    window.debugGallery = function() {
      console.log('=== GALLERY DEBUG INFO ===');

      const container = document.querySelector('.mySwiper');
      const slides = container ? container.querySelectorAll('.swiper-slide') : [];
      const images = container ? container.querySelectorAll('img') : [];

      console.log('Container found:', !!container);
      console.log('Slides count:', slides.length);
      console.log('Images count:', images.length);

      if (container) {
        console.log('Container HTML:', container.innerHTML.substring(0, 500) + '...');

        // Enable debug mode
        container.classList.add('debug-mode');

        images.forEach((img, index) => {
          console.log(`Image ${index + 1}:`);
          console.log('  - Source:', img.src);
          console.log('  - Complete:', img.complete);
          console.log('  - Natural width:', img.naturalWidth);
          console.log('  - Natural height:', img.naturalHeight);
          console.log('  - Display:', window.getComputedStyle(img).display);
          console.log('  - Visibility:', window.getComputedStyle(img).visibility);
          console.log('  - Opacity:', window.getComputedStyle(img).opacity);

          // Force show image for debugging
          img.style.opacity = '1';
          img.style.display = 'block';
          img.style.visibility = 'visible';
        });
      }

      if (window.accountSwiper) {
        console.log('Swiper instance:', window.accountSwiper);
        console.log('Swiper slides:', window.accountSwiper.slides.length);
        window.accountSwiper.update();
      }

      console.log('=== END DEBUG INFO ===');
    };

    // ENHANCED: Auto-run debug if no images are visible after 3 seconds
    setTimeout(() => {
      const visibleImages = document.querySelectorAll('.mySwiper img[style*="opacity: 1"], .mySwiper img:not([style*="opacity"])');
      if (visibleImages.length === 0) {
        console.warn('No visible images detected after 3 seconds, running debug...');
        window.debugGallery();

        // ENHANCED: Force show fallback if still no images
        const swiperWrapper = document.querySelector('.mySwiper .swiper-wrapper');
        if (swiperWrapper && swiperWrapper.children.length === 0) {
          console.warn('No slides found, creating emergency fallback...');
          swiperWrapper.innerHTML = `
            <div class="swiper-slide">
              <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-700/80 to-gray-800/90 rounded-xl border border-gray-600/30">
                <div class="text-center text-gray-300">
                  <div class="w-24 h-24 bg-gray-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-12 h-12 opacity-60" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                  <p class="text-base font-medium mb-2">Đang tải hình ảnh...</p>
                  <p class="text-sm opacity-75">Vui lòng đợi trong giây lát</p>
                </div>
              </div>
            </div>
          `;

          // Reinitialize Swiper if needed
          if (window.accountSwiper) {
            window.accountSwiper.update();
          }
        }
      }
    }, 3000);

    // Initialize Enhanced Fancybox with Gaming Theme
    if (typeof $.fancybox !== 'undefined') {
      $("[data-fancybox]").fancybox({
        buttons: [
          "zoom",
          "slideShow",
          "fullScreen",
          "download",
          "thumbs",
          "close"
        ],
        loop: true,
        protect: true,
        animationEffect: "zoom-in-out",
        animationDuration: 366,
        transitionEffect: "slide",
        transitionDuration: 366,
        slideShow: {
          autoStart: false,
          speed: 3000
        },
        thumbs: {
          autoStart: true,
          hideOnClose: true,
          parentEl: ".fancybox-container",
          axis: "y"
        },
        touch: {
          vertical: true,
          momentum: true
        },
        wheel: "auto",
        infobar: true,
        toolbar: true,
        smallBtn: "auto",
        keyboard: true,
        focus: true,
        closeClickOutside: true,
        parentEl: "body",
        backFocus: true,
        trapFocus: true
      });
    }

    // ENHANCED: Gaming Tooltip System
    function initGamingTooltips() {
      // Remove any existing tooltips
      document.querySelectorAll('.gaming-tooltip-container').forEach(el => el.remove());

      // Initialize gaming tooltips
      document.querySelectorAll('.gaming-tooltip').forEach(element => {
        const tooltipText = element.getAttribute('data-tooltip');
        const tooltipTheme = element.getAttribute('data-tooltip-theme') || 'default';

        if (!tooltipText) return;

        let tooltip = null;
        let showTimeout = null;
        let hideTimeout = null;

        function showTooltip(e) {
          clearTimeout(hideTimeout);

          if (tooltip) {
            tooltip.remove();
          }

          showTimeout = setTimeout(() => {
            tooltip = createTooltip(tooltipText, tooltipTheme);
            document.body.appendChild(tooltip);
            positionTooltip(tooltip, element, e);

            // Animate in
            requestAnimationFrame(() => {
              tooltip.style.opacity = '1';
              tooltip.style.transform = 'translateY(0) scale(1)';
            });
          }, 300);
        }

        function hideTooltip() {
          clearTimeout(showTimeout);

          if (tooltip) {
            hideTimeout = setTimeout(() => {
              tooltip.style.opacity = '0';
              tooltip.style.transform = 'translateY(10px) scale(0.95)';

              setTimeout(() => {
                if (tooltip && tooltip.parentNode) {
                  tooltip.remove();
                  tooltip = null;
                }
              }, 200);
            }, 100);
          }
        }

        function updatePosition(e) {
          if (tooltip) {
            positionTooltip(tooltip, element, e);
          }
        }

        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
        element.addEventListener('mousemove', updatePosition);
      });
    }

    function createTooltip(text, theme) {
      const tooltip = document.createElement('div');
      tooltip.className = 'gaming-tooltip-container';

      // Theme-specific styling
      let themeClasses = '';
      let iconSvg = '';

      switch(theme) {
        case 'character':
          themeClasses = 'border-emerald-500/50 bg-gradient-to-br from-emerald-900/95 to-gray-900/95 shadow-emerald-500/25';
          iconSvg = '<svg class="w-3 h-3 text-emerald-400 mr-1.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/></svg>';
          break;
        case 'weapon':
          themeClasses = 'border-orange-500/50 bg-gradient-to-br from-orange-900/95 to-gray-900/95 shadow-orange-500/25';
          iconSvg = '<svg class="w-3 h-3 text-orange-400 mr-1.5" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/></svg>';
          break;
        case 'skin':
          themeClasses = 'border-purple-500/50 bg-gradient-to-br from-purple-900/95 to-gray-900/95 shadow-purple-500/25';
          iconSvg = '<svg class="w-3 h-3 text-purple-400 mr-1.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/></svg>';
          break;
        default:
          themeClasses = 'border-blue-500/50 bg-gradient-to-br from-blue-900/95 to-gray-900/95 shadow-blue-500/25';
          iconSvg = '<svg class="w-3 h-3 text-blue-400 mr-1.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/></svg>';
      }

      tooltip.innerHTML = `
        <div class="relative px-3 py-2 rounded-lg border backdrop-blur-sm shadow-lg ${themeClasses} transition-all duration-200">
          <div class="flex items-center text-white text-sm font-medium">
            ${iconSvg}
            <span>${text}</span>
          </div>
          <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full">
            <div class="w-2 h-2 bg-gray-900 border-l border-b border-gray-600 transform rotate-45"></div>
          </div>
        </div>
      `;

      tooltip.style.cssText = `
        position: fixed;
        z-index: 9999;
        pointer-events: none;
        opacity: 0;
        transform: translateY(10px) scale(0.95);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      `;

      return tooltip;
    }

    function positionTooltip(tooltip, element, mouseEvent) {
      const rect = element.getBoundingClientRect();
      const tooltipRect = tooltip.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
      let top = rect.top - tooltipRect.height - 10;

      // Adjust horizontal position if tooltip goes off screen
      if (left < 10) {
        left = 10;
      } else if (left + tooltipRect.width > viewportWidth - 10) {
        left = viewportWidth - tooltipRect.width - 10;
      }

      // Adjust vertical position if tooltip goes off screen
      if (top < 10) {
        top = rect.bottom + 10;
        // Flip arrow to top
        const arrow = tooltip.querySelector('.absolute.bottom-0');
        if (arrow) {
          arrow.className = 'absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full';
          arrow.innerHTML = '<div class="w-2 h-2 bg-gray-900 border-t border-r border-gray-600 transform rotate-45"></div>';
        }
      }

      tooltip.style.left = left + 'px';
      tooltip.style.top = top + 'px';
    }

    // Initialize tooltips when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initGamingTooltips);
    } else {
      initGamingTooltips();
    }

    // Reinitialize tooltips if content changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          const hasTooltipElements = Array.from(mutation.addedNodes).some(node =>
            node.nodeType === 1 && (node.classList.contains('gaming-tooltip') || node.querySelector('.gaming-tooltip'))
          );
          if (hasTooltipElements) {
            setTimeout(initGamingTooltips, 100);
          }
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Add gaming visual effects
    const addGamingEffects = () => {
      // Parallax effect for background
      window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.gaming-account-container::before');
        if (parallax) {
          parallax.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
      });

      // Hover effects for gaming cards
      document.querySelectorAll('.gaming-account-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
        });
      });

      // Gaming button effects
      document.querySelectorAll('.gaming-btn-primary').forEach(btn => {
        btn.addEventListener('click', function(e) {
          // Ripple effect
          const ripple = document.createElement('span');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;

          ripple.style.width = ripple.style.height = size + 'px';
          ripple.style.left = x + 'px';
          ripple.style.top = y + 'px';
          ripple.classList.add('gaming-ripple');

          this.appendChild(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });
    };

    // Initialize gaming effects
    addGamingEffects();

    // ===== MODAL FUNCTIONS MODULE =====
    const ModalManager = {
      elements: {},

      init() {
        this.cacheElements();
        this.setupInitialStates();
      },

      cacheElements() {
        this.elements = {
          modal: document.getElementById('purchaseModal'),
          content: document.getElementById('modalContent'),
          backdrop: document.getElementById('modalBackdrop'),
          container: document.getElementById('modalContainer'),
          glow: document.getElementById('modalGlow'),
          particles: document.querySelector('.gaming-particles')
        };
      },

      setupInitialStates() {
        const { content, backdrop, container, glow } = this.elements;
        if (content) {
          content.style.transform = 'translateY(100px) scale(0.9)';
          content.style.opacity = '0';
        }
        if (backdrop) backdrop.style.opacity = '0';
        if (container) container.style.opacity = '0';
        if (glow) glow.style.opacity = '0';
      },

      open() {
        const { modal, content, backdrop, container, glow } = this.elements;

        if (!modal || !content) {
          console.error('Modal elements not found');
          return;
        }

        // Show modal and prevent body scroll
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        document.body.classList.add('modal-open');

        // Animate entrance
        requestAnimationFrame(() => {
          backdrop.style.transition = 'opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
          backdrop.style.opacity = '1';
          container.style.transition = 'opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
          container.style.opacity = '1';
        });

        setTimeout(() => {
          content.style.transition = 'all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)';
          content.style.transform = 'translateY(0) scale(1)';
          content.style.opacity = '1';

          if (glow) {
            setTimeout(() => {
              glow.style.transition = 'opacity 0.8s ease-out';
              glow.style.opacity = '1';
            }, 300);
          }
        }, 100);

        // Focus management and effects
        setTimeout(() => {
          const firstFocusable = modal.querySelector('button[type="submit"], a[href], button:not([disabled])');
          if (firstFocusable) firstFocusable.focus();
        }, 700);

        this.addEventListeners();
        this.startParticles();
      },

      close() {
        const { modal, content, backdrop, container, glow } = this.elements;

        if (!modal || !content) return;

        // Animate exit
        if (glow) {
          glow.style.transition = 'opacity 0.3s ease-out';
          glow.style.opacity = '0';
        }

        content.style.transition = 'all 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53)';
        content.style.transform = 'translateY(50px) scale(0.9)';
        content.style.opacity = '0';

        setTimeout(() => {
          container.style.transition = 'opacity 0.3s ease-out';
          container.style.opacity = '0';
          backdrop.style.transition = 'opacity 0.4s ease-out';
          backdrop.style.opacity = '0';
        }, 100);

        // Hide modal and restore body scroll
        setTimeout(() => {
          modal.classList.add('hidden');
          document.body.style.overflow = '';
          document.body.classList.remove('modal-open');
          this.setupInitialStates();
        }, 500);

        this.removeEventListeners();
        this.stopParticles();
      },

      addEventListeners() {
        document.addEventListener('keydown', this.handleEscape);
        document.addEventListener('touchmove', this.preventDefault, { passive: false });
      },

      removeEventListeners() {
        document.removeEventListener('keydown', this.handleEscape);
        document.removeEventListener('touchmove', this.preventDefault);
      },

      handleEscape(event) {
        if (event.key === 'Escape') ModalManager.close();
      },

      preventDefault(event) {
        event.preventDefault();
      },

      startParticles() {
        const container = this.elements.particles;
        if (!container) return;

        for (let i = 0; i < 20; i++) {
          this.createParticle(container);
        }
      },

      createParticle(container) {
        const particle = document.createElement('div');
        particle.className = 'absolute w-1 h-1 bg-blue-400 rounded-full opacity-30';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animationDuration = (Math.random() * 3 + 2) + 's';
        particle.style.animationDelay = Math.random() * 2 + 's';
        particle.style.animation = `float ${particle.style.animationDuration} infinite ease-in-out ${particle.style.animationDelay}`;

        container.appendChild(particle);
        setTimeout(() => particle.remove(), 5000);
      },

      stopParticles() {
        const container = this.elements.particles;
        if (container) container.innerHTML = '';
      }
    };

    // Global modal functions for HTML onclick compatibility
    window.openPurchaseModal = () => ModalManager.open();
    window.closePurchaseModal = () => ModalManager.close();

    // Initialize modal manager
    ModalManager.init();

    console.log('🎮 Gaming Account Page initialized successfully');
  });
</script>
<?php
    require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/footer.php';
?>
<!-- ENHANCED: Stunning Gaming Purchase Modal with Professional Animations -->
<?php if ($acc['user_id'] == null && $acc['status'] == 1) {?>
<!-- Modal Backdrop with Enhanced Effects -->
<div id="purchaseModal"
     class="fixed inset-0 z-50 hidden overflow-y-auto"
     aria-labelledby="modal-title"
     role="dialog"
     aria-modal="true">

  <!-- Enhanced Background Overlay with Animated Particles -->
  <div class="fixed inset-0 bg-gradient-to-br from-black/90 via-gray-900/95 to-black/90 backdrop-blur-md transition-all duration-500"
       onclick="closePurchaseModal()" id="modalBackdrop">
    <!-- Animated Gaming Particles -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="gaming-particles"></div>
    </div>
  </div>

  <!-- Enhanced Modal Container with Smooth Animations -->
  <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0" id="modalContainer">
    <div class="relative w-full max-w-lg transform transition-all duration-500 ease-out"
         id="modalContent">

      <!-- ENHANCED: Stunning Gaming Modal Card with Glow Effects -->
      <div class="relative bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">

        <!-- Gaming Glow Border Animation -->
        <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-blue-500/20 opacity-0 transition-opacity duration-500" id="modalGlow"></div>

        <!-- Enhanced Background Pattern -->
        <div class="absolute inset-0 opacity-5">
          <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] bg-repeat"></div>
        </div>

        <!-- OPTIMIZED: Compact Gaming Modal Header -->
        <div class="relative p-6 border-b border-gradient-to-r from-blue-500/30 via-purple-500/30 to-blue-500/30">
          <!-- Header Background Glow -->
          <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-blue-500/5"></div>

          <!-- Gaming Icon -->
          <div class="relative text-center mb-4">
            <div class="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full shadow-lg shadow-blue-500/25 mb-3">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
              </svg>
            </div>

            <h2 id="modal-title" class="text-2xl md:text-3xl font-bold text-center mb-3 bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
              Xác Nhận Mua Tài Khoản
            </h2>
          </div>

          <!-- Account Info with Enhanced Styling -->
          <div class="relative text-center space-y-2">
            <div class="inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-gray-800 to-gray-700 rounded-full border border-gray-600 shadow-lg">
              <span class="text-gray-300 text-base mr-2">Mã số</span>
              <span class="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">#<?php echo $slug ?></span>
            </div>

            <div class="flex items-center justify-center space-x-2 text-gray-400">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
              </svg>
              <span class="text-xs font-medium">Danh Mục:</span>
              <span class="text-blue-400 font-semibold text-sm"><?php echo $service['name']?></span>
            </div>
          </div>

          <!-- OPTIMIZED: Compact Close Button -->
          <button type="button"
                  onclick="closePurchaseModal()"
                  class="absolute top-4 right-4 w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110 hover:rotate-90 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-gray-800 shadow-lg shadow-red-500/25"
                  aria-label="Đóng modal">
            <svg class="w-4 h-4 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        <!-- OPTIMIZED: Compact Gaming Modal Body -->
        <div class="relative p-6 space-y-4">

          <!-- OPTIMIZED: Compact Transaction Info Card -->
          <div class="relative">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg blur-sm"></div>
            <div class="relative bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-lg border border-gray-600/50 p-4 shadow-xl">
              <div class="flex items-center mb-3">
                <div class="w-6 h-6 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-md flex items-center justify-center mr-2">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <h3 class="text-base font-semibold text-white">Thông Tin Giao Dịch</h3>
              </div>

              <div class="space-y-2">
                <div class="flex justify-between items-center py-2 px-3 bg-gray-700/20 rounded-md">
                  <span class="text-gray-300 text-sm flex items-center">
                    <svg class="w-3 h-3 mr-1.5 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    Phí thanh toán
                  </span>
                  <span class="text-sm font-semibold text-emerald-400 bg-emerald-400/10 px-2 py-0.5 rounded-md">Miễn phí</span>
                </div>
                <div class="flex justify-between items-center py-2 px-3 bg-gray-700/20 rounded-md">
                  <span class="text-gray-300 text-sm flex items-center">
                    <svg class="w-3 h-3 mr-1.5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
                    </svg>
                    Phương thức
                  </span>
                  <span class="text-sm font-semibold text-blue-400 bg-blue-400/10 px-2 py-0.5 rounded-md">Tự động</span>
                </div>
              </div>
            </div>
          </div>

          <!-- OPTIMIZED: Compact User Balance Card -->
          <div class="relative">
            <div class="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg blur-sm"></div>
            <div class="relative bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-lg border border-gray-600/50 p-4 shadow-xl">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-md flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                  <div>
                    <p class="text-gray-300 text-sm">Số dư của bạn</p>
                    <p class="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                      <?=format_cash($auth['money'])?><sup class="text-xs">đ</sup>
                    </p>
                  </div>
                </div>
                <div class="text-right">
                  <div class="w-8 h-8 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- OPTIMIZED: Compact Purchase Form -->
          <form submit-ajax="duogxaolin" action="/api/service/account" method="POST" class="space-y-4">
            <input type="hidden" name="type" value="payment">
            <input type="hidden" name="id" value="<?php echo $acc['id'] ?>">
            <input type="hidden" name="code" value="<?php echo $acc['slug'] ?>">

            <!-- OPTIMIZED: Compact Discount Information -->
            <?php if ($acc['discount'] != 0) {?>
              <div class="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-sm rounded-lg border border-gray-600/50 p-3 shadow-lg">
                <div class="grid grid-cols-2 gap-3 text-center">
                  <div>
                    <div class="text-amber-400 text-xs font-medium mb-1">Giá Gốc</div>
                    <div class="text-amber-400 text-base font-bold">
                      <?php echo format_cash($acc['money']) ?><sup class="text-xs">đ</sup>
                    </div>
                  </div>
                  <div>
                    <div class="text-emerald-400 text-xs font-medium mb-1">Giảm Giá</div>
                    <div class="text-emerald-400 text-base font-bold">
                      <?php echo $acc['discount'] ?>%
                    </div>
                  </div>
                </div>
              </div>
            <?php }?>

            <!-- OPTIMIZED: Compact Pricing Summary -->
            <div class="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-sm rounded-lg border border-gray-600/50 p-3 shadow-lg">
              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-gray-300 text-sm">Tổng tiền</span>
                  <span class="text-gray-300 font-semibold text-sm">
                    <?php echo format_cash($acc['money']) ?><sup class="text-xs">đ</sup>
                  </span>
                </div>
                <div class="border-t border-gray-600 pt-2">
                  <div class="flex justify-between items-center">
                    <span class="text-white font-semibold text-sm uppercase">Tổng thanh toán</span>
                    <span class="gaming-accent-text font-bold text-lg">
                      <?php echo format_cash($acc['money'] - $acc['money'] * $acc['discount'] / 100) ?>
                      <sup class="text-xs">đ</sup>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- OPTIMIZED: Compact Action Buttons -->
            <div class="pt-2">
              <?php if(isset($_SESSION['username'])) { ?>
                <button type="submit" class="gaming-btn-primary w-full">
                  <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Xác Nhận Mua
                </button>
              <?php } else { ?>
                <a href="/customer/login" class="gaming-btn-primary w-full block text-center">
                  <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  Đăng Nhập
                </a>
              <?php }?>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<?php }?>